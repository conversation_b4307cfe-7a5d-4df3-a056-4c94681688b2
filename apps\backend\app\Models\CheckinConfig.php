<?php

namespace App\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CheckinConfig extends BaseModel
{
    use HasFactory;

    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    protected $fillable = [
        'attachable_type',
        'attachable_id',
        'checkin_time',
        'status',
        'is_photo',
        'location',
        'longitude',
        'latitude',
        'location_range',
        'created_by',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'attachable_id' => 'integer',
        'checkin_time' => 'integer',
        'status' => 'integer',
        'is_photo' => 'integer',
        'location_range' => 'integer',
        'created_by' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer'
    ];

    /**
     * 获取关联的业务实体
     */
    public function attachable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 打卡用户
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'checkin_config_users', 'checkin_config_id', 'user_id');
    }

    public function records()
    {
        return $this->hasMany(CheckinRecord::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 同步打卡人员
     */
    public function syncUsers(array $userIds): void
    {
        $this->users()->sync($userIds, ['created_at' => now()->timestamp, 'updated_at' => now()->timestamp]);
    }
}
