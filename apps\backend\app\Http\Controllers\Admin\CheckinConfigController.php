<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CheckinConfigRequest;
use App\Models\CheckinConfig;
use App\Services\CheckinService;
use Illuminate\Http\Request;
use App\Http\Resources\Admin\CheckinConfigResource;
use Illuminate\Support\Facades\Validator;

/**
 * @group 考勤配置
 */
class CheckinConfigController extends Controller
{
    public function __construct(private CheckinService $checkinService)
    {
    }

    /**
     * 获取考勤配置列表
     *
     * @queryParam attachable_type string 所属模块 Example: lifecycles
     * @queryParam status int 状态(0-禁用,1-启用) Example: 1
     * @queryParam keyword string 搜索关键词 Example: 早班
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页记录数 Example: 15
     */
    public function index(Request $request)
    {
        $configs = $this->checkinService->getConfigs($request->all());
        return CheckinConfigResource::collection($configs);
    }

    /**
     * 创建考勤配置
     *
     * @bodyParam attachable_type string required 所属模块 Example: lifecycles
     * @bodyParam attachable_id integer required 关联业务ID Example: 1
     * @bodyParam checkin_time int required 打卡时间 Example: 1629936000
     * @bodyParam status int 状态(0-禁用,1-启用) Example: 1
     * @bodyParam is_photo int 是否拍照(0-否,1-是) Example: 1
     * @bodyParam location string 打卡地点 Example: 公司大门
     * @bodyParam longitude decimal 打卡位置经度 Example: 39.9042
     * @bodyParam latitude decimal 打卡位置纬度 Example: 116.4074
     * @bodyParam location_range int 打卡范围(米) Example: 100
     * @bodyParam user_ids array required 打卡人员ID列表 Example: [1,2,3]
     */
    public function store(CheckinConfigRequest $request)
    {
        $config = $this->checkinService->createConfig($request->validated());
        return (new CheckinConfigResource($config));
    }

    /**
     * 更新考勤配置
     *
     * @urlParam checkinConfig int required 配置ID Example: 1
     * @bodyParam attachable_type string 所属模块 Example: lifecycles
     * @bodyParam attachable_id integer 关联业务ID Example: 1
     * @bodyParam checkin_time int 打卡时间 Example: 1629936000
     * @bodyParam status int 状态(0-禁用,1-启用) Example: 1
     * @bodyParam is_photo int 是否拍照(0-否,1-是) Example: 1
     * @bodyParam location string 打卡地点 Example: 公司大门
     * @bodyParam longitude decimal 打卡位置经度 Example: 39.9042
     * @bodyParam latitude decimal 打卡位置纬度 Example: 116.4074
     * @bodyParam location_range int 打卡范围(米) Example: 100
     * @bodyParam user_ids array 打卡人员ID列表 Example: [1,2,3]
     */
    public function update(CheckinConfig $config, CheckinConfigRequest $request)
    {
        $config = $this->checkinService->updateConfig($config->id, $request->validated());
        return (new CheckinConfigResource($config));
    }

    /**
     * 删除考勤配置
     *
     * @urlParam checkinConfig int required 配置ID Example: 1
     */
    public function destroy($id)
    {
        // $this->checkinService->deleteConfig($id);
        return response()->noContent();
    }

    /**
     * 获取考勤配置详情
     *
     * @urlParam checkinConfig int required 配置ID Example: 1
     */
    public function show(CheckinConfig $config)
    {
        $config->load(['users:id,nickname']);
        return new CheckinConfigResource($config);
    }

    /**
     * 开启/关闭考勤配置
     *
     * @urlParam checkinConfig int required 配置ID Example: 1
     * @bodyParam status int 状态(0-禁用,1-启用) Example: 1
     */
    public function switch(CheckinConfig $config, Request $request)
    {
        // 判断是否存在考勤配置
        if (!isset($config->id)) {
            return $this->error('考勤配置不存在', 404);
        }

        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1',
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        // 如果状态为1，则关闭，否则开启
        $status = $request->status;
        $config->status = $status;
        $config->save();

        return (new CheckinConfigResource($config));
    }
}
