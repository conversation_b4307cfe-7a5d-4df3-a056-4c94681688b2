openapi: 3.0.3
info:
  title: '设备云管理系统 API Documentation'
  description: ''
  version: 1.0.0
servers:
  -
    url: 'http://localhost:8005'
tags:
  -
    name: Endpoints
    description: ''
  -
    name: 二维码生成
    description: ''
  -
    name: 分类管理
    description: "\n系统分类管理接口"
  -
    name: 品牌管理
    description: ''
  -
    name: 地区管理
    description: "\n省市区地区数据管理接口"
  -
    name: 字典管理
    description: "\n系统字典配置管理接口"
  -
    name: 授权
    description: "\n管理员认证相关接口"
  -
    name: 操作日志管理
    description: "\n系统操作日志查询接口"
  -
    name: 标签管理
    description: "\n管理系统标签库"
  -
    name: 生命周期管理
    description: "\n管理设备生命周期记录"
  -
    name: 用户管理
    description: ''
  -
    name: 相关方管理
    description: ''
  -
    name: 考勤记录
    description: ''
  -
    name: 考勤配置
    description: ''
  -
    name: 菜单管理
    description: "\n系统菜单管理接口"
  -
    name: 角色管理
    description: "\n管理系统角色"
  -
    name: 资产管理
    description: ''
  -
    name: 配置管理
    description: "\n系统配置管理接口"
  -
    name: 附件管理
    description: 附件的上传、下载、删除等操作
components:
  securitySchemes:
    default:
      type: http
      scheme: bearer
      description: 'You can retrieve your token by visiting your dashboard and clicking <b>Generate API token</b>.'
security:
  -
    default: []
paths:
  '/api/admin/import/{type}/{attachment_id}':
    post:
      summary: 统一创建导入任务
      operationId: ''
      description: "路径参数:\n- type: 导入类型，支持 asset/category/entity/user\n- attachment: 附件ID"
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
    parameters:
      -
        in: path
        name: type
        description: ''
        example: architecto
        required: true
        schema:
          type: string
      -
        in: path
        name: attachment_id
        description: 'The ID of the attachment.'
        example: 16
        required: true
        schema:
          type: integer
  '/api/admin/import/{type}/tasks/{task}':
    get:
      summary: 统一查询导入任务状态
      operationId: ''
      description: "路径参数:\n- type: 导入类型\n- task: 任务ID"
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
    parameters:
      -
        in: path
        name: type
        description: ''
        example: architecto
        required: true
        schema:
          type: string
      -
        in: path
        name: task
        description: 'The task.'
        example: architecto
        required: true
        schema:
          type: string
  /api/admin/qrcode/generate:
    post:
      summary: 生成二维码
      operationId: ''
      description: ''
      parameters: []
      responses:
        201:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  qrcode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
                properties:
                  qrcode:
                    type: string
                    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
      tags:
        - 二维码生成
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: 要生成二维码的内容
                  example: '1222'
                  nullable: false
                size:
                  type: integer
                  description: 二维码大小，默认300，范围50-1000
                  example: 300
                  nullable: true
                color:
                  type: string
                  description: 二维码颜色，十六进制颜色码
                  example: '#ff0000'
                  nullable: true
                background_color:
                  type: string
                  description: 背景颜色，十六进制颜色码
                  example: '#ffffff'
                  nullable: true
                margin:
                  type: integer
                  description: 二维码边距，默认1，范围0-50
                  example: 1
                  nullable: true
      security: []
  /api/admin/categories:
    get:
      summary: 获取分类树
      operationId: ''
      description: 获取所有分类的树形结构数据
      parameters:
        -
          in: query
          name: status
          description: '状态筛选（1启用 0禁用）'
          example: 1
          required: false
          schema:
            type: integer
            description: '状态筛选（1启用 0禁用）'
            example: 1
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 分类管理
    post:
      summary: 创建分类
      operationId: ''
      description: 创建新的分类
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 分类管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 分类名称
                  example: 电子设备
                  nullable: false
                code:
                  type: string
                  description: 分类编码
                  example: electronic
                  nullable: false
                parent_id:
                  type: integer
                  description: 父级ID，默认0
                  example: 0
                  nullable: false
                sort:
                  type: integer
                  description: 排序值，默认0
                  example: 0
                  nullable: false
                status:
                  type: integer
                  description: 状态，默认1
                  example: 1
                  nullable: false
                remark:
                  type: string
                  description: 备注信息
                  example: 电子设备分类
                  nullable: true
              required:
                - name
                - code
  '/api/admin/categories/{id}':
    put:
      summary: 更新分类
      operationId: ''
      description: 更新指定的分类信息
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 分类管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 分类名称
                  example: 电子设备
                  nullable: false
                code:
                  type: string
                  description: 分类编码
                  example: electronic
                  nullable: false
                parent_id:
                  type: integer
                  description: 父级ID
                  example: 0
                  nullable: false
                sort:
                  type: integer
                  description: 排序值
                  example: 0
                  nullable: false
                status:
                  type: integer
                  description: 状态
                  example: 1
                  nullable: false
                remark:
                  type: string
                  description: 备注信息
                  example: 电子设备分类
                  nullable: true
              required:
                - name
                - code
    delete:
      summary: 删除分类
      operationId: ''
      description: 删除指定的分类（分类下存在子分类时无法删除）
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 分类管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the category.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: category
        description: 分类ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/categories/children/{parentId}':
    get:
      summary: 获取子分类
      operationId: ''
      description: 获取指定分类的直接子分类列表
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 分类管理
    parameters:
      -
        in: path
        name: parentId
        description: 父级分类ID，传0获取顶级分类
        example: 0
        required: true
        schema:
          type: integer
  /api/admin/categories/export/template:
    get:
      summary: 导出分类Excel模板
      operationId: Excel
      description: 导出分类导入的Excel模板文件
      parameters:
        -
          in: query
          name: with_sample
          description: 是否包含示例数据
          example: true
          required: false
          schema:
            type: boolean
            description: 是否包含示例数据
            example: true
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 分类管理
  /api/admin/brands:
    get:
      summary: 获取系统所有品牌列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: keyword
          description: 搜索关键字
          example: 华为
          required: false
          schema:
            type: string
            description: 搜索关键字
            example: 华为
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                example:
                  -
                    id: 61
                    name: 'GE Healthcare'
                    entity_id: 61
                    entity_name: 广东安健科技股份有限公司
                    display_name: '广东安健科技股份有限公司 - GE Healthcare'
                    description: 'Quidem nostrum qui commodi incidunt iure odit.'
                    sort_order: 765
                  -
                    id: 62
                    name: 理邦仪器
                    entity_id: 62
                    entity_name: 苏州奥普拓激光科技有限公司
                    display_name: '苏州奥普拓激光科技有限公司 - 理邦仪器'
                    description: 'Officia est dignissimos neque blanditiis odio veritatis excepturi.'
                    sort_order: 721
      tags:
        - 品牌管理
  /api/admin/regions/tree:
    get:
      summary: 获取地区树形结构
      operationId: ''
      description: 获取完整的省市区三级树形结构数据，用于级联选择器
      parameters:
        -
          in: query
          name: deep
          description: 获取的层级深度（可选，默认3级）
          example: 3
          required: false
          schema:
            type: integer
            description: 获取的层级深度（可选，默认3级）
            example: 3
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
  '/api/admin/regions/children/{parentId}':
    get:
      summary: 获取指定父级的子地区
      operationId: ''
      description: 用于懒加载获取子地区数据
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
    parameters:
      -
        in: path
        name: parentId
        description: 父级地区ID
        example: 11
        required: true
        schema:
          type: integer
  '/api/admin/regions/path/{code}':
    get:
      summary: 根据地区代码获取完整路径
      operationId: ''
      description: 根据区县代码获取省市区完整路径信息
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
    parameters:
      -
        in: path
        name: code
        description: 地区代码
        example: '110101000000'
        required: true
        schema:
          type: string
  /api/admin/regions/search:
    get:
      summary: 搜索地区
      operationId: ''
      description: 根据关键词搜索地区，支持名称和拼音搜索
      parameters:
        -
          in: query
          name: keyword
          description: 搜索关键词
          example: 北京
          required: true
          schema:
            type: string
            description: 搜索关键词
            example: 北京
            nullable: false
        -
          in: query
          name: limit
          description: 返回结果数量限制（默认20，最大50）
          example: 20
          required: false
          schema:
            type: integer
            description: 返回结果数量限制（默认20，最大50）
            example: 20
            nullable: false
        -
          in: query
          name: deep
          description: 搜索的层级（0省1市2区县，默认搜索所有层级）
          example: 2
          required: false
          schema:
            type: integer
            description: 搜索的层级（0省1市2区县，默认搜索所有层级）
            example: 2
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                keyword:
                  type: string
                  description: 'Must be at least 1 character.'
                  example: bngzmiyvdljnikhw
                  nullable: false
                limit:
                  type: integer
                  description: 'Must be at least 1. Must not be greater than 50.'
                  example: 22
                  nullable: false
                deep:
                  type: integer
                  description: ''
                  example: '2'
                  nullable: false
                  enum:
                    - '0'
                    - '1'
                    - '2'
              required:
                - keyword
  /api/admin/regions/provinces:
    get:
      summary: 获取省份列表
      operationId: ''
      description: 获取所有省级行政区列表
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
  '/api/admin/regions/cities/{provinceId}':
    get:
      summary: 获取指定省份的城市列表
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
    parameters:
      -
        in: path
        name: provinceId
        description: 省份ID
        example: 11
        required: true
        schema:
          type: integer
  '/api/admin/regions/districts/{cityId}':
    get:
      summary: 获取指定城市的区县列表
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 地区管理
    parameters:
      -
        in: path
        name: cityId
        description: 城市ID
        example: 1101
        required: true
        schema:
          type: integer
  /api/admin/dictionary/categories:
    get:
      summary: 获取字典分类列表
      operationId: ''
      description: 获取所有字典分类，支持条件筛选
      parameters:
        -
          in: query
          name: name
          description: 分类名称
          example: 设备类型
          required: false
          schema:
            type: string
            description: 分类名称
            example: 设备类型
            nullable: false
        -
          in: query
          name: code
          description: 分类编码
          example: device_type
          required: false
          schema:
            type: string
            description: 分类编码
            example: device_type
            nullable: false
        -
          in: query
          name: is_enabled
          description: 是否启用
          example: true
          required: false
          schema:
            type: boolean
            description: 是否启用
            example: true
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
    post:
      summary: 创建字典分类
      operationId: ''
      description: 创建新的字典分类
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: '分类编码，只能包含字母、数字和下划线，且不能以数字开头. Must match the regex /^[a-zA-Z_][a-zA-Z0-9_]*$/. Must not be greater than 50 characters.'
                  example: device_type
                  nullable: false
                name:
                  type: string
                  description: '分类名称. Must not be greater than 100 characters.'
                  example: 设备类型
                  nullable: false
                description:
                  type: string
                  description: '分类描述. Must not be greater than 500 characters.'
                  example: 设备类型分类
                  nullable: true
                sort:
                  type: integer
                  description: '排序值，数值越小越靠前. Must be at least 0.'
                  example: 1
                  nullable: true
                is_enabled:
                  type: boolean
                  description: 是否启用.
                  example: false
                  nullable: false
              required:
                - code
                - name
  '/api/admin/dictionary/categories/{id}':
    put:
      summary: 更新字典分类
      operationId: ''
      description: 更新指定的字典分类信息
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: '分类编码，只能包含字母、数字和下划线，且不能以数字开头. Must match the regex /^[a-zA-Z_][a-zA-Z0-9_]*$/. Must not be greater than 50 characters.'
                  example: device_type
                  nullable: false
                name:
                  type: string
                  description: '分类名称. Must not be greater than 100 characters.'
                  example: 设备类型
                  nullable: false
                description:
                  type: string
                  description: '分类描述. Must not be greater than 500 characters.'
                  example: 设备类型分类
                  nullable: true
                sort:
                  type: integer
                  description: '排序值，数值越小越靠前. Must be at least 0.'
                  example: 1
                  nullable: true
                is_enabled:
                  type: boolean
                  description: 是否启用.
                  example: false
                  nullable: false
              required:
                - code
                - name
    delete:
      summary: 删除字典分类
      operationId: ''
      description: 删除指定的字典分类（分类下存在字典项时无法删除）
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the category.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: category
        description: 字典分类ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/dictionary/items:
    get:
      summary: 获取字典项列表
      operationId: ''
      description: 获取所有字典项，支持条件筛选
      parameters:
        -
          in: query
          name: category_id
          description: 分类ID
          example: 1
          required: false
          schema:
            type: integer
            description: 分类ID
            example: 1
            nullable: false
        -
          in: query
          name: code
          description: 字典编码
          example: desktop
          required: false
          schema:
            type: string
            description: 字典编码
            example: desktop
            nullable: false
        -
          in: query
          name: value
          description: 字典值
          example: 台式机
          required: false
          schema:
            type: string
            description: 字典值
            example: 台式机
            nullable: false
        -
          in: query
          name: is_enabled
          description: 是否启用
          example: true
          required: false
          schema:
            type: boolean
            description: 是否启用
            example: true
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
    post:
      summary: 创建字典项
      operationId: ''
      description: 在指定分类下创建新的字典项
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                category_id:
                  type: string
                  description: '所属字典分类ID. The <code>id</code> of an existing record in the dictionary_categories table.'
                  example: 1
                  nullable: false
                code:
                  type: string
                  description: '字典编码，同一分类下唯一. Must not be greater than 50 characters.'
                  example: desktop
                  nullable: false
                value:
                  type: string
                  description: '字典值，用于显示. Must not be greater than 200 characters.'
                  example: 台式机
                  nullable: false
                label:
                  type: string
                  description: '显示标签，可选的备用显示文本. Must not be greater than 200 characters.'
                  example: 台式计算机
                  nullable: true
                sort:
                  type: integer
                  description: '排序值，数值越小越靠前. Must be at least 0.'
                  example: 1
                  nullable: true
                color:
                  type: string
                  description: '颜色值，格式为#开头的6位16进制. Must not be greater than 50 characters.'
                  example: '#FF5733'
                  nullable: true
                icon:
                  type: string
                  description: '图标标识. Must not be greater than 50 characters.'
                  example: el-icon-monitor
                  nullable: true
                config:
                  type: object
                  description: 扩展配置，JSON格式.
                  example:
                    key: value
                  nullable: true
                  properties: {  }
                remark:
                  type: string
                  description: '备注说明. Must not be greater than 500 characters.'
                  example: 用于标识台式计算机类型
                  nullable: true
                is_enabled:
                  type: boolean
                  description: 是否启用.
                  example: false
                  nullable: false
              required:
                - category_id
                - code
                - value
  '/api/admin/dictionary/items/{id}':
    put:
      summary: 更新字典项
      operationId: ''
      description: 更新指定的字典项信息
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                category_id:
                  type: string
                  description: '所属字典分类ID. The <code>id</code> of an existing record in the dictionary_categories table.'
                  example: 1
                  nullable: false
                code:
                  type: string
                  description: '字典编码，同一分类下唯一. Must not be greater than 50 characters.'
                  example: desktop
                  nullable: false
                value:
                  type: string
                  description: '字典值，用于显示. Must not be greater than 200 characters.'
                  example: 台式机
                  nullable: false
                label:
                  type: string
                  description: '显示标签，可选的备用显示文本. Must not be greater than 200 characters.'
                  example: 台式计算机
                  nullable: true
                sort:
                  type: integer
                  description: '排序值，数值越小越靠前. Must be at least 0.'
                  example: 1
                  nullable: true
                color:
                  type: string
                  description: '颜色值，格式为#开头的6位16进制. Must not be greater than 50 characters.'
                  example: '#FF5733'
                  nullable: true
                icon:
                  type: string
                  description: '图标标识. Must not be greater than 50 characters.'
                  example: el-icon-monitor
                  nullable: true
                config:
                  type: object
                  description: 扩展配置，JSON格式.
                  example:
                    key: value
                  nullable: true
                  properties: {  }
                remark:
                  type: string
                  description: '备注说明. Must not be greater than 500 characters.'
                  example: 用于标识台式计算机类型
                  nullable: true
                is_enabled:
                  type: boolean
                  description: 是否启用.
                  example: false
                  nullable: false
              required:
                - category_id
                - code
                - value
    delete:
      summary: 删除字典项
      operationId: ''
      description: 删除指定的字典项
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the item.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: item
        description: 字典项ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/dictionary/code/{categoryCode}':
    get:
      summary: 根据分类编码获取字典项
      operationId: ''
      description: 获取指定分类编码下的所有启用的字典项
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 字典管理
    parameters:
      -
        in: path
        name: categoryCode
        description: 分类编码
        example: device_type
        required: true
        schema:
          type: string
  /api/admin/login:
    post:
      summary: 登录
      operationId: ''
      description: 管理员登录接口，验证用户名和密码后返回访问令牌
      parameters: []
      responses:
        422:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: 账号或密码错误
                  errors:
                    account:
                      - 账号或密码错误
                properties:
                  message:
                    type: string
                    example: 账号或密码错误
                  errors:
                    type: object
                    properties:
                      account:
                        type: array
                        example:
                          - 账号或密码错误
                        items:
                          type: string
      tags:
        - 授权
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: 登录账号
                  example: '***********'
                  nullable: false
                password:
                  type: string
                  description: 密码
                  example: password123
                  nullable: false
              required:
                - account
                - password
      security: []
  /api/admin/logout:
    post:
      summary: 退出登录
      operationId: ''
      description: 撤销当前访问令牌，退出登录状态
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 授权
  /api/admin/operation-logs:
    get:
      summary: 获取操作日志列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: user_name
          description: 用户名搜索
          example: admin
          required: false
          schema:
            type: string
            description: 用户名搜索
            example: admin
            nullable: false
        -
          in: query
          name: ip
          description: IP地址搜索
          example: ***********
          required: false
          schema:
            type: string
            description: IP地址搜索
            example: ***********
            nullable: false
        -
          in: query
          name: method
          description: 请求方法筛选
          example: POST
          required: false
          schema:
            type: string
            description: 请求方法筛选
            example: POST
            nullable: false
        -
          in: query
          name: path
          description: 请求路径搜索
          example: /api/users
          required: false
          schema:
            type: string
            description: 请求路径搜索
            example: /api/users
            nullable: false
        -
          in: query
          name: operation_type
          description: 操作类型筛选
          example: create
          required: false
          schema:
            type: string
            description: 操作类型筛选
            example: create
            nullable: false
        -
          in: query
          name: target_type
          description: 目标类型筛选
          example: Asset
          required: false
          schema:
            type: string
            description: 目标类型筛选
            example: Asset
            nullable: false
        -
          in: query
          name: menu_name
          description: 菜单名称搜索
          example: 资产管理
          required: false
          schema:
            type: string
            description: 菜单名称搜索
            example: 资产管理
            nullable: false
        -
          in: query
          name: start_time
          description: 开始时间
          example: '2024-01-01 00:00:00'
          required: false
          schema:
            type: string
            description: 开始时间
            example: '2024-01-01 00:00:00'
            nullable: false
        -
          in: query
          name: end_time
          description: 结束时间
          example: '2024-12-31 23:59:59'
          required: false
          schema:
            type: string
            description: 结束时间
            example: '2024-12-31 23:59:59'
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 20
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 20
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 操作日志管理
  '/api/admin/operation-logs/{operationLog_id}':
    get:
      summary: 获取操作日志详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 操作日志管理
    parameters:
      -
        in: path
        name: operationLog_id
        description: 'The ID of the operationLog.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: id
        description: 日志ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/operation-logs/stats/operation-types:
    get:
      summary: 获取操作类型统计
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 操作日志管理
  /api/admin/operation-logs/stats/menus:
    get:
      summary: 获取菜单操作统计
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 操作日志管理
  /api/admin/tags:
    get:
      summary: 获取标签列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页数量.
          example: 20
          required: false
          schema:
            type: integer
            description: 每页数量.
            example: 20
            nullable: false
        -
          in: query
          name: name
          description: 标签名称搜索.
          example: 重要
          required: false
          schema:
            type: string
            description: 标签名称搜索.
            example: 重要
            nullable: false
        -
          in: query
          name: category
          description: 标签分类筛选.
          example: 重要
          required: false
          schema:
            type: string
            description: 标签分类筛选.
            example: 重要
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 21
                      name: 待巡查保养
                      category: 状态
                    -
                      id: 22
                      name: 紧急维修
                      category: 状态
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 21
                        name: 待巡查保养
                        category: 状态
                      -
                        id: 22
                        name: 紧急维修
                        category: 状态
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 21
                        name:
                          type: string
                          example: 待巡查保养
                        category:
                          type: string
                          example: 状态
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 标签管理
    post:
      summary: 创建标签
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 标签管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 标签名称.
                  example: 重要
                  nullable: false
                category:
                  type: string
                  description: 标签分类.
                  example: 重要
                  nullable: true
              required:
                - name
  /api/admin/tags/all:
    get:
      summary: 获取所有标签（用于选择器）
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 标签管理
  '/api/admin/tags/{id}':
    put:
      summary: 更新标签
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 标签管理
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 标签名称.
                  example: 重要
                  nullable: false
                category:
                  type: string
                  description: 标签分类.
                  example: 重要
                  nullable: true
    delete:
      summary: 删除标签
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 标签管理
    parameters:
      -
        in: path
        name: id
        description: 标签ID.
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/lifecycles:
    get:
      summary: 获取生命周期列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页数量.
          example: 20
          required: false
          schema:
            type: integer
            description: 每页数量.
            example: 20
            nullable: false
        -
          in: query
          name: asset_id
          description: 资产ID筛选.
          example: '1'
          required: false
          schema:
            type: string
            description: 资产ID筛选.
            example: '1'
            nullable: false
        -
          in: query
          name: type
          description: 类型筛选.
          example: installation
          required: false
          schema:
            type: string
            description: 类型筛选.
            example: installation
            nullable: false
        -
          in: query
          name: start_date
          description: 开始日期筛选.
          example: '2024-01-01'
          required: false
          schema:
            type: string
            description: 开始日期筛选.
            example: '2024-01-01'
            nullable: false
        -
          in: query
          name: end_date
          description: 结束日期筛选.
          example: '2024-12-31'
          required: false
          schema:
            type: string
            description: 结束日期筛选.
            example: '2024-12-31'
            nullable: false
        -
          in: query
          name: initiator_id
          description: 发起人ID筛选.
          example: 1
          required: false
          schema:
            type: integer
            description: 发起人ID筛选.
            example: 1
            nullable: false
        -
          in: query
          name: acceptance_entity_id
          description: 验收相关方ID筛选.
          example: 1
          required: false
          schema:
            type: integer
            description: 验收相关方ID筛选.
            example: 1
            nullable: false
        -
          in: query
          name: tag_ids
          description: 标签ID逗号分隔.
          example: '10,11'
          required: false
          schema:
            type: string
            description: 标签ID逗号分隔.
            example: '10,11'
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: null
                      type: null
                      date: null
                      initiator: null
                      content: null
                      acceptance_entity: null
                      acceptance_personnel: null
                      acceptance_time: null
                      is_checked: null
                      follow_ups_tags: []
                      progress: 0
                      created_at: null
                      updated_at: null
                    -
                      id: null
                      type: null
                      date: null
                      initiator: null
                      content: null
                      acceptance_entity: null
                      acceptance_personnel: null
                      acceptance_time: null
                      is_checked: null
                      follow_ups_tags: []
                      progress: 0
                      created_at: null
                      updated_at: null
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: null
                        type: null
                        date: null
                        initiator: null
                        content: null
                        acceptance_entity: null
                        acceptance_personnel: null
                        acceptance_time: null
                        is_checked: null
                        follow_ups_tags: []
                        progress: 0
                        created_at: null
                        updated_at: null
                      -
                        id: null
                        type: null
                        date: null
                        initiator: null
                        content: null
                        acceptance_entity: null
                        acceptance_personnel: null
                        acceptance_time: null
                        is_checked: null
                        follow_ups_tags: []
                        progress: 0
                        created_at: null
                        updated_at: null
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: null
                        type:
                          type: string
                          example: null
                        date:
                          type: string
                          example: null
                        initiator:
                          type: string
                          example: null
                        content:
                          type: string
                          example: null
                        acceptance_entity:
                          type: string
                          example: null
                        acceptance_personnel:
                          type: string
                          example: null
                        acceptance_time:
                          type: string
                          example: null
                        is_checked:
                          type: string
                          example: null
                        follow_ups_tags:
                          type: array
                          example: []
                        progress:
                          type: integer
                          example: 0
                        created_at:
                          type: string
                          example: null
                        updated_at:
                          type: string
                          example: null
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 生命周期管理
    post:
      summary: 创建生命周期
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                assets:
                  type: array
                  description: 资产数组.
                  example:
                    - 1
                    - 2
                  items:
                    type: string
                type:
                  type: string
                  description: 类型（字典值）.
                  example: installation
                  nullable: false
                date:
                  type: string
                  description: 日期时间戳.
                  example: '1716028800'
                  nullable: false
                initiator_id:
                  type: integer
                  description: 发起人ID.
                  example: 1
                  nullable: false
                content:
                  type: string
                  description: 内容.
                  example: 采购了一批新的服务器设备
                  nullable: false
                assistants:
                  type: array
                  description: 协助人员ID数组.
                  example:
                    - 1
                  items:
                    type: integer
                acceptance_entity_id:
                  type: integer
                  description: 验收相关方ID.
                  example: 1
                  nullable: false
                acceptance_personnel_id:
                  type: integer
                  description: 验收人员ID.
                  example: 1
                  nullable: false
                acceptance_time:
                  type: string
                  description: 验收时间戳.
                  example: '1716028800'
                  nullable: false
                attachments:
                  type: array
                  description: 附件ID数组.
                  example: []
                  items:
                    type: integer
                tag_ids:
                  type: array
                  description: 标签ID数组.
                  example:
                    - 17
                    - 18
                  items:
                    type: integer
              required:
                - assets
                - type
                - date
                - initiator_id
                - content
                - assistants
                - acceptance_entity_id
                - acceptance_personnel_id
                - acceptance_time
  '/api/admin/lifecycles/{id}':
    get:
      summary: 获取生命周期详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
    put:
      summary: 更新生命周期
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                assets:
                  type: array
                  description: 资产数组.
                  example:
                    - 1
                    - 2
                  items:
                    type: string
                type:
                  type: string
                  description: 类型（字典值）.
                  example: installation
                  nullable: false
                date:
                  type: string
                  description: 日期时间戳.
                  example: '1716028800'
                  nullable: false
                initiator_id:
                  type: integer
                  description: 发起人ID.
                  example: 1
                  nullable: false
                content:
                  type: string
                  description: 内容.
                  example: 采购了一批新的服务器设备
                  nullable: false
                assistants:
                  type: array
                  description: 协助人员ID数组.
                  example:
                    - 2
                    - 3
                  items:
                    type: integer
                acceptance_entity_id:
                  type: integer
                  description: 验收相关方ID.
                  example: 1
                  nullable: false
                acceptance_personnel_id:
                  type: integer
                  description: 验收人员ID.
                  example: 1
                  nullable: false
                acceptance_time:
                  type: string
                  description: 验收时间戳.
                  example: '1716028800'
                  nullable: false
                attachments:
                  type: array
                  description: 附件ID数组.
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: integer
                tag_ids:
                  type: array
                  description: 标签ID数组.
                  example:
                    - 10
                    - 12
                  items:
                    type: integer
    delete:
      summary: 删除生命周期
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
    parameters:
      -
        in: path
        name: id
        description: 生命周期ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/lifecycles/{id}/accept':
    put:
      summary: 生命周期验收
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                is_checked:
                  type: integer
                  description: 是否验收.
                  example: 1
                  nullable: false
              required:
                - is_checked
    parameters:
      -
        in: path
        name: id
        description: 生命周期ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/lifecycles/entities/{entityId}/acceptance-personnel':
    get:
      summary: 获取验收人员列表
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
    parameters:
      -
        in: path
        name: entityId
        description: 验收相关方ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/lifecycles/{id}/tags':
    put:
      summary: 同步标签
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                tag_ids:
                  type: array
                  description: 标签ID数组.
                  example:
                    - 10
                    - 12
                  items:
                    type: integer
              required:
                - tag_ids
    parameters:
      -
        in: path
        name: id
        description: 生命周期ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/lifecycles/{lifecycleId}/follow-ups':
    post:
      summary: 创建跟进记录
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date:
                  type: string
                  description: 日期.
                  example: '2024-01-16'
                  nullable: false
                person_id:
                  type: integer
                  description: 跟进人ID（必须是协助人员之一）.
                  example: 2
                  nullable: false
                content:
                  type: string
                  description: 内容.
                  example: 已联系供应商确认发货时间
                  nullable: false
                attachments:
                  type: array
                  description: 附件ID数组.
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: integer
                tag_ids:
                  type: array
                  description: 'The <code>id</code> of an existing record in the tags table.'
                  example: null
                  items:
                    type: string
                tag_id:
                  type: integer
                  description: 标签ID.
                  example: 10
                  nullable: false
              required:
                - date
                - person_id
                - content
                - tag_id
    parameters:
      -
        in: path
        name: lifecycleId
        description: 生命周期ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/lifecycles/{lifecycleId}/follow-ups/{id}':
    get:
      summary: 获取跟进记录详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
    put:
      summary: 更新跟进记录
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                date:
                  type: string
                  description: 日期.
                  example: '2024-01-16'
                  nullable: false
                person_id:
                  type: integer
                  description: 跟进人ID（必须是协助人员之一）.
                  example: 2
                  nullable: false
                content:
                  type: string
                  description: 内容.
                  example: 已联系供应商确认发货时间
                  nullable: false
                attachments:
                  type: array
                  description: 附件ID数组.
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: integer
                tag_ids:
                  type: array
                  description: 'The <code>id</code> of an existing record in the tags table.'
                  example: null
                  items:
                    type: string
                tag_id:
                  type: integer
                  description: 标签ID.
                  example: 10
                  nullable: false
    delete:
      summary: 删除跟进记录
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 生命周期管理
    parameters:
      -
        in: path
        name: lifecycleId
        description: 生命周期ID.
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: id
        description: 跟进记录ID.
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/users:
    get:
      summary: 获取用户列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: keyword
          description: 搜索关键词（用户名、手机号、邮箱）
          example: admin
          required: false
          schema:
            type: string
            description: 搜索关键词（用户名、手机号、邮箱）
            example: admin
            nullable: false
        -
          in: query
          name: status
          description: 用户状态（enable/disable）
          example: enable
          required: false
          schema:
            type: string
            description: 用户状态（enable/disable）
            example: enable
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 20
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 20
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: null
                      account: null
                      nickname: null
                      email: null
                      avatar: null
                      avatar_id: null
                      status: null
                      status_label: 禁用
                      is_super_admin: null
                      created_at: null
                      updated_at: null
                    -
                      id: null
                      account: null
                      nickname: null
                      email: null
                      avatar: null
                      avatar_id: null
                      status: null
                      status_label: 禁用
                      is_super_admin: null
                      created_at: null
                      updated_at: null
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: null
                        account: null
                        nickname: null
                        email: null
                        avatar: null
                        avatar_id: null
                        status: null
                        status_label: 禁用
                        is_super_admin: null
                        created_at: null
                        updated_at: null
                      -
                        id: null
                        account: null
                        nickname: null
                        email: null
                        avatar: null
                        avatar_id: null
                        status: null
                        status_label: 禁用
                        is_super_admin: null
                        created_at: null
                        updated_at: null
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: null
                        account:
                          type: string
                          example: null
                        nickname:
                          type: string
                          example: null
                        email:
                          type: string
                          example: null
                        avatar:
                          type: string
                          example: null
                        avatar_id:
                          type: string
                          example: null
                        status:
                          type: string
                          example: null
                        status_label:
                          type: string
                          example: 禁用
                        is_super_admin:
                          type: string
                          example: null
                        created_at:
                          type: string
                          example: null
                        updated_at:
                          type: string
                          example: null
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 用户管理
    post:
      summary: 创建用户
      operationId: ''
      description: ''
      parameters: []
      responses:
        201:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    id: 1
                    account: '***********'
                    nickname: testuser
                    email: <EMAIL>
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      account:
                        type: string
                        example: '***********'
                      nickname:
                        type: string
                        example: testuser
                      email:
                        type: string
                        example: <EMAIL>
      tags:
        - 用户管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                  description: 用户昵称
                  example: testuser
                  nullable: false
                email:
                  type: string
                  description: 邮箱
                  example: <EMAIL>
                  nullable: true
                account:
                  type: required
                  description: 'string 用户账号'
                  example: '***********'
                  nullable: false
                status:
                  type: string
                  description: 状态（enable/disable）
                  example: enable
                  nullable: false
                avatar_id:
                  type: integer
                  description: 'The <code>id</code> of an existing record in the attachments table.'
                  example: 16
                  nullable: true
                password:
                  type: string
                  description: 密码（最少6位）
                  example: password123
                  nullable: false
              required:
                - password
  '/api/admin/users/{id}':
    get:
      summary: 获取用户详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 用户管理
    put:
      summary: 更新用户
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 用户管理
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                  description: 昵称
                  example: testuser
                  nullable: false
                email:
                  type: string
                  description: 邮箱
                  example: <EMAIL>
                  nullable: true
                account:
                  type: string
                  description: 账号
                  example: '***********'
                  nullable: false
                status:
                  type: string
                  description: 状态（enable/disable）
                  example: enable
                  nullable: false
                avatar_id:
                  type: integer
                  description: 'The <code>id</code> of an existing record in the attachments table.'
                  example: 16
                  nullable: true
                password:
                  type: string
                  description: 密码（为空则不更新）
                  example: newpassword
                  nullable: true
    delete:
      summary: 删除用户
      operationId: ''
      description: ''
      parameters: []
      responses:
        204:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example: {  }
                properties: {  }
      tags:
        - 用户管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the user.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: user
        description: 用户ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/users/export/template:
    get:
      summary: 导出用户Excel模板
      operationId: Excel
      description: 导出包含字段：用户账号、密码、用户昵称、邮箱、用户角色
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 用户管理
  '/api/admin/users/{user_id}/roles/assign':
    post:
      summary: 为用户分配角色（追加式）
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 用户管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role_ids:
                  type: array
                  description: 角色ID数组.
                  example:
                    - 1
                    - 2
                  items:
                    type: string
              required:
                - role_ids
    parameters:
      -
        in: path
        name: user_id
        description: 'The ID of the user.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: user
        description: 用户ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/users/{user_id}/roles/sync':
    put:
      summary: 同步用户角色（覆盖式）
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 用户管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role_ids:
                  type: array
                  description: 角色ID数组（将完全替换用户当前的角色）.
                  example:
                    - 1
                    - 2
                  items:
                    type: string
              required:
                - role_ids
    parameters:
      -
        in: path
        name: user_id
        description: 'The ID of the user.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: user
        description: 用户ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/users/{user_id}/roles/remove':
    delete:
      summary: 移除用户角色
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 用户管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role_ids:
                  type: array
                  description: 要移除的角色ID数组.
                  example:
                    - 1
                    - 2
                  items:
                    type: string
              required:
                - role_ids
    parameters:
      -
        in: path
        name: user_id
        description: 'The ID of the user.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: user
        description: 用户ID.
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/entities:
    get:
      summary: 获取相关方列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: name
          description: 相关方名称搜索
          example: 测试公司
          required: false
          schema:
            type: string
            description: 相关方名称搜索
            example: 测试公司
            nullable: false
        -
          in: query
          name: tax_number
          description: 税号搜索
          example: 91110108MA01A2B3C4
          required: false
          schema:
            type: string
            description: 税号搜索
            example: 91110108MA01A2B3C4
            nullable: false
        -
          in: query
          name: keywords
          description: 特征词搜索
          example: 科技
          required: false
          schema:
            type: string
            description: 特征词搜索
            example: 科技
            nullable: false
        -
          in: query
          name: keyword
          description: 通用搜索关键词（同时搜索名称、税号、特征词）
          example: 测试
          required: false
          schema:
            type: string
            description: 通用搜索关键词（同时搜索名称、税号、特征词）
            example: 测试
            nullable: false
        -
          in: query
          name: entity_type
          description: 相关方类型（字典code）
          example: manufacturer
          required: false
          schema:
            type: string
            description: 相关方类型（字典code）
            example: manufacturer
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 20
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 20
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 63
                      name: 北京康泰医疗设备有限公司
                      tax_number: 91090933724500949X
                      entity_type: manufacturer
                      address: "38862 Ferne Locks Suite 058\nChristianshire, IA 97161"
                      phone: '+12063337339'
                      keywords: 'deleniti distinctio eum'
                      remark: 'Id aut libero aliquam veniam.'
                      created_by: 1
                      updated_by: 1
                      created_at: **********
                      updated_at: **********
                    -
                      id: 64
                      name: 苏州奥普拓激光科技有限公司
                      tax_number: 91445197905807351X
                      entity_type: supplier
                      address: "2669 Wolff Trail\nBeierburgh, VA 78637"
                      phone: '+****************'
                      keywords: 'ut et recusandae'
                      remark: 'Rerum ex repellendus assumenda et.'
                      created_by: 1
                      updated_by: 1
                      created_at: **********
                      updated_at: **********
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 63
                        name: 北京康泰医疗设备有限公司
                        tax_number: 91090933724500949X
                        entity_type: manufacturer
                        address: "38862 Ferne Locks Suite 058\nChristianshire, IA 97161"
                        phone: '+12063337339'
                        keywords: 'deleniti distinctio eum'
                        remark: 'Id aut libero aliquam veniam.'
                        created_by: 1
                        updated_by: 1
                        created_at: **********
                        updated_at: **********
                      -
                        id: 64
                        name: 苏州奥普拓激光科技有限公司
                        tax_number: 91445197905807351X
                        entity_type: supplier
                        address: "2669 Wolff Trail\nBeierburgh, VA 78637"
                        phone: '+****************'
                        keywords: 'ut et recusandae'
                        remark: 'Rerum ex repellendus assumenda et.'
                        created_by: 1
                        updated_by: 1
                        created_at: **********
                        updated_at: **********
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 63
                        name:
                          type: string
                          example: 北京康泰医疗设备有限公司
                        tax_number:
                          type: string
                          example: 91090933724500949X
                        entity_type:
                          type: string
                          example: manufacturer
                        address:
                          type: string
                          example: "38862 Ferne Locks Suite 058\nChristianshire, IA 97161"
                        phone:
                          type: string
                          example: '+12063337339'
                        keywords:
                          type: string
                          example: 'deleniti distinctio eum'
                        remark:
                          type: string
                          example: 'Id aut libero aliquam veniam.'
                        created_by:
                          type: integer
                          example: 1
                        updated_by:
                          type: integer
                          example: 1
                        created_at:
                          type: integer
                          example: **********
                        updated_at:
                          type: integer
                          example: **********
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 相关方管理
    post:
      summary: 创建相关方
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 相关方名称
                  example: 测试科技有限公司
                  nullable: false
                tax_number:
                  type: string
                  description: 税号
                  example: 91110108MA01A2B3C4
                  nullable: true
                entity_type:
                  type: string
                  description: 相关方类型（字典code）
                  example: manufacturer
                  nullable: false
                address:
                  type: string
                  description: 地址
                  example: 北京市海淀区中关村大街1号
                  nullable: true
                phone:
                  type: string
                  description: 联系电话
                  example: 010-12345678
                  nullable: true
                keywords:
                  type: string
                  description: 特征词（10字以内）
                  example: 科技创新
                  nullable: true
                remark:
                  type: string
                  description: 备注
                  example: architecto
                  nullable: true
                contacts:
                  type: array
                  description: 联系人列表
                  example:
                    - architecto
                  items:
                    type: string
                    nullable: true
                brands:
                  type: array
                  description: 品牌列表
                  example:
                    - architecto
                  items:
                    type: string
                    nullable: true
                attachments:
                  type: array
                  description: 'The <code>id</code> of an existing record in the attachments table.'
                  example:
                    - 16
                  items:
                    type: integer
              required:
                - name
                - entity_type
  /api/admin/entities/export-template:
    get:
      summary: 导出相关方模板
      operationId: ''
      description: 导出包含所有字段的Excel模板文件，用于相关方批量导入
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
  '/api/admin/entities/{id}':
    get:
      summary: 获取相关方详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
    put:
      summary: 更新相关方
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 相关方名称
                  example: 测试科技有限公司
                  nullable: false
                tax_number:
                  type: string
                  description: 税号
                  example: 91110108MA01A2B3C4
                  nullable: true
                entity_type:
                  type: string
                  description: 相关方类型（字典code）
                  example: manufacturer
                  nullable: false
                address:
                  type: string
                  description: 地址
                  example: 北京市海淀区中关村大街1号
                  nullable: true
                phone:
                  type: string
                  description: 联系电话
                  example: 010-12345678
                  nullable: true
                keywords:
                  type: string
                  description: 特征词（10字以内）
                  example: 科技创新
                  nullable: true
                remark:
                  type: string
                  description: 备注
                  example: architecto
                  nullable: true
                contacts:
                  type: array
                  description: 联系人列表
                  example:
                    - architecto
                  items:
                    type: string
                    nullable: true
                brands:
                  type: array
                  description: 品牌列表
                  example:
                    - architecto
                  items:
                    type: string
                    nullable: true
                attachments:
                  type: array
                  description: 'The <code>id</code> of an existing record in the attachments table.'
                  example:
                    - 16
                  items:
                    type: integer
              required:
                - name
                - entity_type
    delete:
      summary: 删除相关方
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the entity.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: entity
        description: 相关方ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/entities/{entity_id}/contacts':
    get:
      summary: 获取相关方的联系人列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 10
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 10
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 32
                      entity_id: 65
                      name: 'Morgan Hirthe'
                      phone: ************
                      position: 临床工程师
                      department: 客服部
                      created_by: 1
                      updated_by: 1
                      created_at: **********
                      updated_at: **********
                    -
                      id: 33
                      entity_id: 66
                      name: 'Dr. Alan Green'
                      phone: ************
                      position: 设备科工程师
                      department: 采购部
                      created_by: 1
                      updated_by: 1
                      created_at: **********
                      updated_at: **********
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 10
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 32
                        entity_id: 65
                        name: 'Morgan Hirthe'
                        phone: ************
                        position: 临床工程师
                        department: 客服部
                        created_by: 1
                        updated_by: 1
                        created_at: **********
                        updated_at: **********
                      -
                        id: 33
                        entity_id: 66
                        name: 'Dr. Alan Green'
                        phone: ************
                        position: 设备科工程师
                        department: 采购部
                        created_by: 1
                        updated_by: 1
                        created_at: **********
                        updated_at: **********
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 32
                        entity_id:
                          type: integer
                          example: 65
                        name:
                          type: string
                          example: 'Morgan Hirthe'
                        phone:
                          type: string
                          example: ************
                        position:
                          type: string
                          example: 临床工程师
                        department:
                          type: string
                          example: 客服部
                        created_by:
                          type: integer
                          example: 1
                        updated_by:
                          type: integer
                          example: 1
                        created_at:
                          type: integer
                          example: **********
                        updated_at:
                          type: integer
                          example: **********
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 10
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 相关方管理
    post:
      summary: 创建联系人
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 联系人姓名
                  example: 张三
                  nullable: false
                phone:
                  type: string
                  description: 联系电话
                  example: '***********'
                  nullable: false
                position:
                  type: string
                  description: 职位
                  example: 总经理
                  nullable: true
                department:
                  type: string
                  description: 部门
                  example: 管理部
                  nullable: true
              required:
                - name
                - phone
    parameters:
      -
        in: path
        name: entity_id
        description: 'The ID of the entity.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: entity
        description: 相关方ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/entities/{entity_id}/contacts/{id}':
    put:
      summary: 更新联系人
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 联系人姓名
                  example: 张三
                  nullable: false
                phone:
                  type: string
                  description: 联系电话
                  example: '***********'
                  nullable: false
                position:
                  type: string
                  description: 职位
                  example: 总经理
                  nullable: true
                department:
                  type: string
                  description: 部门
                  example: 管理部
                  nullable: true
              required:
                - name
                - phone
    delete:
      summary: 删除联系人
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
    parameters:
      -
        in: path
        name: entity_id
        description: 'The ID of the entity.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: id
        description: 'The ID of the contact.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: entity
        description: 相关方ID
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: contact
        description: 联系人ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/entities/{entity_id}/brands':
    get:
      summary: 获取相关方的品牌列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 10
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 10
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 63
                      entity_id: 67
                      name: 'GE Healthcare'
                      logo: null
                      logo_id: null
                      description: 'Quidem nostrum qui commodi incidunt iure odit.'
                      sort_order: 765
                      created_by: 1
                      updated_by: 1
                      created_at: **********
                      updated_at: **********
                    -
                      id: 64
                      entity_id: 68
                      name: 理邦仪器
                      logo: null
                      logo_id: null
                      description: 'Officia est dignissimos neque blanditiis odio veritatis excepturi.'
                      sort_order: 721
                      created_by: 1
                      updated_by: 1
                      created_at: **********
                      updated_at: **********
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 10
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 63
                        entity_id: 67
                        name: 'GE Healthcare'
                        logo: null
                        logo_id: null
                        description: 'Quidem nostrum qui commodi incidunt iure odit.'
                        sort_order: 765
                        created_by: 1
                        updated_by: 1
                        created_at: **********
                        updated_at: **********
                      -
                        id: 64
                        entity_id: 68
                        name: 理邦仪器
                        logo: null
                        logo_id: null
                        description: 'Officia est dignissimos neque blanditiis odio veritatis excepturi.'
                        sort_order: 721
                        created_by: 1
                        updated_by: 1
                        created_at: **********
                        updated_at: **********
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 63
                        entity_id:
                          type: integer
                          example: 67
                        name:
                          type: string
                          example: 'GE Healthcare'
                        logo:
                          type: string
                          example: null
                        logo_id:
                          type: string
                          example: null
                        description:
                          type: string
                          example: 'Quidem nostrum qui commodi incidunt iure odit.'
                        sort_order:
                          type: integer
                          example: 765
                        created_by:
                          type: integer
                          example: 1
                        updated_by:
                          type: integer
                          example: 1
                        created_at:
                          type: integer
                          example: **********
                        updated_at:
                          type: integer
                          example: **********
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 10
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 相关方管理
    post:
      summary: 创建品牌
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 品牌名称
                  example: 华为
                  nullable: false
                description:
                  type: string
                  description: 品牌描述
                  example: 全球领先的信息与通信技术解决方案供应商
                  nullable: true
                logo_id:
                  type: integer
                  description: 品牌Logo附件ID
                  example: 1
                  nullable: true
                sort_order:
                  type: integer
                  description: 排序顺序
                  example: 100
                  nullable: true
              required:
                - name
    parameters:
      -
        in: path
        name: entity_id
        description: 'The ID of the entity.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: entity
        description: 相关方ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/entities/{entity_id}/brands/{id}':
    put:
      summary: 更新品牌
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 品牌名称
                  example: 华为
                  nullable: false
                description:
                  type: string
                  description: 品牌描述
                  example: 全球领先的信息与通信技术解决方案供应商
                  nullable: true
                logo_id:
                  type: integer
                  description: 品牌Logo附件ID
                  example: 1
                  nullable: true
                sort_order:
                  type: integer
                  description: 排序顺序
                  example: 100
                  nullable: true
              required:
                - name
    delete:
      summary: 删除品牌
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 相关方管理
    parameters:
      -
        in: path
        name: entity_id
        description: 'The ID of the entity.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: id
        description: 'The ID of the brand.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: entity
        description: 相关方ID
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: brand
        description: 品牌ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/checkin-records:
    get:
      summary: 获取打卡记录列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: config_id
          description: 打卡配置ID
          example: 1
          required: false
          schema:
            type: integer
            description: 打卡配置ID
            example: 1
            nullable: false
        -
          in: query
          name: user_id
          description: 用户ID
          example: 1
          required: false
          schema:
            type: integer
            description: 用户ID
            example: 1
            nullable: false
        -
          in: query
          name: status
          description: '状态(0-正常,1-异常)'
          example: 0
          required: false
          schema:
            type: integer
            description: '状态(0-正常,1-异常)'
            example: 0
            nullable: false
        -
          in: query
          name: date_start
          description: 开始日期
          example: '2025-08-01'
          required: false
          schema:
            type: string
            description: 开始日期
            example: '2025-08-01'
            nullable: false
        -
          in: query
          name: date_end
          description: 结束日期
          example: '2025-08-31'
          required: false
          schema:
            type: string
            description: 结束日期
            example: '2025-08-31'
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页记录数
          example: 15
          required: false
          schema:
            type: integer
            description: 每页记录数
            example: 15
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤记录
    post:
      summary: 打卡操作
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤记录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                checkin_config_id:
                  type: integer
                  description: 打卡配置ID
                  example: 1
                  nullable: false
                location:
                  type: string
                  description: 打卡地点
                  example: 公司大门
                  nullable: true
                position:
                  type: string
                  description: 打卡位置经纬度
                  example: '39.9042,116.4074'
                  nullable: true
                location_range:
                  type: integer
                  description: 打卡位置范围
                  example: 50
                  nullable: true
                attachment_id:
                  type: integer
                  description: 打卡照片附件ID
                  example: 1
                  nullable: true
                content:
                  type: string
                  description: 打卡备注
                  example: 正常打卡
                  nullable: true
              required:
                - checkin_config_id
  '/api/admin/checkin-records/{id}':
    get:
      summary: 获取打卡记录详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤记录
    parameters:
      -
        in: path
        name: id
        description: 记录ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/checkin-configs:
    get:
      summary: 获取考勤配置列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: attachable_type
          description: 所属模块
          example: lifecycles
          required: false
          schema:
            type: string
            description: 所属模块
            example: lifecycles
            nullable: false
        -
          in: query
          name: status
          description: '状态(0-禁用,1-启用)'
          example: 1
          required: false
          schema:
            type: integer
            description: '状态(0-禁用,1-启用)'
            example: 1
            nullable: false
        -
          in: query
          name: keyword
          description: 搜索关键词
          example: 早班
          required: false
          schema:
            type: string
            description: 搜索关键词
            example: 早班
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页记录数
          example: 15
          required: false
          schema:
            type: integer
            description: 每页记录数
            example: 15
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤配置
    post:
      summary: 创建考勤配置
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤配置
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                attachable_type:
                  type: string
                  description: 所属模块
                  example: lifecycles
                  nullable: false
                attachable_id:
                  type: integer
                  description: 关联业务ID
                  example: 1
                  nullable: false
                location:
                  type: string
                  description: 打卡地点
                  example: 公司大门
                  nullable: true
                longitude:
                  type: decimal
                  description: 打卡位置经度
                  example: '39.9042'
                  nullable: true
                latitude:
                  type: decimal
                  description: 打卡位置纬度
                  example: '116.4074'
                  nullable: true
                checkin_time:
                  type: integer
                  description: 打卡时间
                  example: 1629936000
                  nullable: false
                location_range:
                  type: integer
                  description: 打卡范围(米)
                  example: 100
                  nullable: true
                status:
                  type: integer
                  description: '状态(0-禁用,1-启用)'
                  example: 1
                  nullable: false
                is_photo:
                  type: integer
                  description: '是否拍照(0-否,1-是)'
                  example: 1
                  nullable: false
                user_ids:
                  type: array
                  description: 打卡人员ID列表
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
              required:
                - attachable_type
                - attachable_id
                - checkin_time
                - user_ids
  '/api/admin/checkin-configs/{checkinConfig}':
    put:
      summary: 更新考勤配置
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤配置
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                attachable_type:
                  type: string
                  description: 所属模块
                  example: lifecycles
                  nullable: false
                attachable_id:
                  type: integer
                  description: 关联业务ID
                  example: 1
                  nullable: false
                location:
                  type: string
                  description: 打卡地点
                  example: 公司大门
                  nullable: true
                longitude:
                  type: decimal
                  description: 打卡位置经度
                  example: '39.9042'
                  nullable: true
                latitude:
                  type: decimal
                  description: 打卡位置纬度
                  example: '116.4074'
                  nullable: true
                checkin_time:
                  type: integer
                  description: 打卡时间
                  example: 1629936000
                  nullable: false
                location_range:
                  type: integer
                  description: 打卡范围(米)
                  example: 100
                  nullable: true
                status:
                  type: integer
                  description: '状态(0-禁用,1-启用)'
                  example: 1
                  nullable: false
                is_photo:
                  type: integer
                  description: '是否拍照(0-否,1-是)'
                  example: 1
                  nullable: false
                user_ids:
                  type: array
                  description: 打卡人员ID列表
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
    get:
      summary: 获取考勤配置详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤配置
    parameters:
      -
        in: path
        name: checkinConfig
        description: 配置ID
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/checkin-configs/{checkinConfig}/switch':
    put:
      summary: 开启/关闭考勤配置
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 考勤配置
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  description: '状态(0-禁用,1-启用)'
                  example: 1
                  nullable: false
    parameters:
      -
        in: path
        name: checkinConfig
        description: 配置ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/menus:
    get:
      summary: 获取菜单列表
      operationId: ''
      description: 获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  menuList:
                    -
                      id: 1
                      parent_id: 0
                      name: User
                      path: /user
                      component: User
                      title: 用户管理
                      icon: user
                      label: user
                      sort: 1
                      is_hide: false
                      is_hide_tab: false
                      link: 'https://www.baidu.com'
                      is_iframe: false
                      keep_alive: true
                      is_first_level: false
                      fixed_tab: false
                      active_path: /user
                      is_full_page: false
                      show_badge: false
                      show_text_badge: new
                      status: true
                      meta:
                        title: 用户管理
                        icon: user
                        keepAlive: true
                        showBadge: false
                        showTextBadge: new
                        isHide: false
                        isHideTab: false
                        link: 'https://www.baidu.com'
                        isIframe: false
                        authList:
                          -
                            title: 用户列表
                            authMark: 'user:list'
                        isFirstLevel: false
                        fixedTab: false
                        activePath: /user
                        isFullPage: false
                properties:
                  menuList:
                    type: array
                    example:
                      -
                        id: 1
                        parent_id: 0
                        name: User
                        path: /user
                        component: User
                        title: 用户管理
                        icon: user
                        label: user
                        sort: 1
                        is_hide: false
                        is_hide_tab: false
                        link: 'https://www.baidu.com'
                        is_iframe: false
                        keep_alive: true
                        is_first_level: false
                        fixed_tab: false
                        active_path: /user
                        is_full_page: false
                        show_badge: false
                        show_text_badge: new
                        status: true
                        meta:
                          title: 用户管理
                          icon: user
                          keepAlive: true
                          showBadge: false
                          showTextBadge: new
                          isHide: false
                          isHideTab: false
                          link: 'https://www.baidu.com'
                          isIframe: false
                          authList:
                            -
                              title: 用户列表
                              authMark: 'user:list'
                          isFirstLevel: false
                          fixedTab: false
                          activePath: /user
                          isFullPage: false
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        parent_id:
                          type: integer
                          example: 0
                        name:
                          type: string
                          example: User
                        path:
                          type: string
                          example: /user
                        component:
                          type: string
                          example: User
                        title:
                          type: string
                          example: 用户管理
                        icon:
                          type: string
                          example: user
                        label:
                          type: string
                          example: user
                        sort:
                          type: integer
                          example: 1
                        is_hide:
                          type: boolean
                          example: false
                        is_hide_tab:
                          type: boolean
                          example: false
                        link:
                          type: string
                          example: 'https://www.baidu.com'
                        is_iframe:
                          type: boolean
                          example: false
                        keep_alive:
                          type: boolean
                          example: true
                        is_first_level:
                          type: boolean
                          example: false
                        fixed_tab:
                          type: boolean
                          example: false
                        active_path:
                          type: string
                          example: /user
                        is_full_page:
                          type: boolean
                          example: false
                        show_badge:
                          type: boolean
                          example: false
                        show_text_badge:
                          type: string
                          example: new
                        status:
                          type: boolean
                          example: true
                        meta:
                          type: object
                          properties:
                            title:
                              type: string
                              example: 用户管理
                            icon:
                              type: string
                              example: user
                            keepAlive:
                              type: boolean
                              example: true
                            showBadge:
                              type: boolean
                              example: false
                            showTextBadge:
                              type: string
                              example: new
                            isHide:
                              type: boolean
                              example: false
                            isHideTab:
                              type: boolean
                              example: false
                            link:
                              type: string
                              example: 'https://www.baidu.com'
                            isIframe:
                              type: boolean
                              example: false
                            authList:
                              type: array
                              example:
                                -
                                  title: 用户列表
                                  authMark: 'user:list'
                              items:
                                type: object
                                properties:
                                  title:
                                    type: string
                                    example: 用户列表
                                  authMark:
                                    type: string
                                    example: 'user:list'
                            isFirstLevel:
                              type: boolean
                              example: false
                            fixedTab:
                              type: boolean
                              example: false
                            activePath:
                              type: string
                              example: /user
                            isFullPage:
                              type: boolean
                              example: false
      tags:
        - 菜单管理
    post:
      summary: 创建菜单
      operationId: ''
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    id: 1
                    parent_id: 0
                    name: User
                    path: /user
                    component: User
                    title: 用户管理
                    icon: user
                    label: user
                    sort: 1
                    is_hide: false
                    is_hide_tab: false
                    link: 'https://www.baidu.com'
                    is_iframe: false
                    keep_alive: true
                    is_first_level: false
                    fixed_tab: false
                    active_path: /user
                    is_full_page: false
                    show_badge: false
                    show_text_badge: new
                    status: true
                    meta:
                      title: 用户管理
                      icon: user
                      keepAlive: true
                      showBadge: false
                      showTextBadge: new
                      isHide: false
                      isHideTab: false
                      link: 'https://www.baidu.com'
                      isIframe: false
                      authList:
                        -
                          title: 用户列表
                          authMark: 'user:list'
                      isFirstLevel: false
                      fixedTab: false
                      activePath: /user
                      isFullPage: false
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      parent_id:
                        type: integer
                        example: 0
                      name:
                        type: string
                        example: User
                      path:
                        type: string
                        example: /user
                      component:
                        type: string
                        example: User
                      title:
                        type: string
                        example: 用户管理
                      icon:
                        type: string
                        example: user
                      label:
                        type: string
                        example: user
                      sort:
                        type: integer
                        example: 1
                      is_hide:
                        type: boolean
                        example: false
                      is_hide_tab:
                        type: boolean
                        example: false
                      link:
                        type: string
                        example: 'https://www.baidu.com'
                      is_iframe:
                        type: boolean
                        example: false
                      keep_alive:
                        type: boolean
                        example: true
                      is_first_level:
                        type: boolean
                        example: false
                      fixed_tab:
                        type: boolean
                        example: false
                      active_path:
                        type: string
                        example: /user
                      is_full_page:
                        type: boolean
                        example: false
                      show_badge:
                        type: boolean
                        example: false
                      show_text_badge:
                        type: string
                        example: new
                      status:
                        type: boolean
                        example: true
                      meta:
                        type: object
                        properties:
                          title:
                            type: string
                            example: 用户管理
                          icon:
                            type: string
                            example: user
                          keepAlive:
                            type: boolean
                            example: true
                          showBadge:
                            type: boolean
                            example: false
                          showTextBadge:
                            type: string
                            example: new
                          isHide:
                            type: boolean
                            example: false
                          isHideTab:
                            type: boolean
                            example: false
                          link:
                            type: string
                            example: 'https://www.baidu.com'
                          isIframe:
                            type: boolean
                            example: false
                          authList:
                            type: array
                            example:
                              -
                                title: 用户列表
                                authMark: 'user:list'
                            items:
                              type: object
                              properties:
                                title:
                                  type: string
                                  example: 用户列表
                                authMark:
                                  type: string
                                  example: 'user:list'
                          isFirstLevel:
                            type: boolean
                            example: false
                          fixedTab:
                            type: boolean
                            example: false
                          activePath:
                            type: string
                            example: /user
                          isFullPage:
                            type: boolean
                            example: false
      tags:
        - 菜单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                parent_id:
                  type: integer
                  description: 父级菜单ID
                  example: 1
                  nullable: true
                name:
                  type: string
                  description: 菜单名称
                  example: 用户管理
                  nullable: true
                path:
                  type: string
                  description: 菜单路径
                  example: /user
                  nullable: true
                component:
                  type: string
                  description: 组件路径
                  example: User
                  nullable: true
                title:
                  type: string
                  description: 菜单标题
                  example: 用户管理
                  nullable: false
                icon:
                  type: string
                  description: 菜单图标
                  example: user
                  nullable: true
                label:
                  type: string
                  description: 权限标识
                  example: user
                  nullable: true
                sort:
                  type: integer
                  description: 排序
                  example: 1
                  nullable: false
                is_hide:
                  type: boolean
                  description: 是否隐藏
                  example: false
                  nullable: false
                is_hide_tab:
                  type: boolean
                  description: 是否在标签页隐藏
                  example: false
                  nullable: false
                link:
                  type: string
                  description: 外部链接
                  example: 'https://www.baidu.com'
                  nullable: true
                is_iframe:
                  type: boolean
                  description: 是否为iframe
                  example: false
                  nullable: false
                keep_alive:
                  type: boolean
                  description: 是否缓存
                  example: true
                  nullable: false
                is_first_level:
                  type: boolean
                  description: 是否为一级菜单
                  example: false
                  nullable: false
                fixed_tab:
                  type: boolean
                  description: 是否固定标签页
                  example: false
                  nullable: false
                active_path:
                  type: string
                  description: 激活菜单路径
                  example: /user
                  nullable: true
                is_full_page:
                  type: boolean
                  description: 是否全屏页面
                  example: false
                  nullable: false
                show_badge:
                  type: boolean
                  description: 是否显示徽章
                  example: false
                  nullable: false
                show_text_badge:
                  type: string
                  description: 文本徽章内容
                  example: new
                  nullable: true
                status:
                  type: boolean
                  description: 状态
                  example: true
                  nullable: false
                permissions:
                  type: array
                  description: 权限列表
                  example:
                    -
                      title: 用户列表
                      auth_mark: 'user:list'
                      sort: 1
                  items:
                    type: string
              required:
                - name
                - path
                - title
  /api/admin/menus/tree:
    get:
      summary: 获取菜单树
      operationId: ''
      description: 用于选择父级菜单
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 1
                      parent_id: 0
                      name: User
                      path: /user
                      component: User
                      title: 用户管理
                      icon: user
                      label: user
                      sort: 1
                      is_hide: false
                      is_hide_tab: false
                      link: 'https://www.baidu.com'
                      is_iframe: false
                      keep_alive: true
                      is_first_level: false
                      fixed_tab: false
                      active_path: /user
                      is_full_page: false
                      show_badge: false
                      show_text_badge: new
                      status: true
                      meta:
                        title: 用户管理
                        icon: user
                        keepAlive: true
                        showBadge: false
                        showTextBadge: new
                        isHide: false
                        isHideTab: false
                        link: 'https://www.baidu.com'
                        isIframe: false
                        authList:
                          -
                            title: 用户列表
                            authMark: 'user:list'
                        isFirstLevel: false
                        fixedTab: false
                        activePath: /user
                        isFullPage: false
                      children:
                        -
                          id: 2
                          parent_id: 1
                          name: UserList
                          path: /user/list
                          component: UserList
                          title: 用户列表
                          icon: user
                          label: 'user:list'
                          sort: 1
                          is_hide: false
                          is_hide_tab: false
                          link: 'https://www.baidu.com'
                          is_iframe: false
                          keep_alive: true
                          is_first_level: false
                          fixed_tab: false
                          active_path: /user/list
                          is_full_page: false
                          show_badge: false
                          show_text_badge: new
                          status: true
                          meta:
                            title: 用户列表
                            icon: user
                            keepAlive: true
                            showBadge: false
                            showTextBadge: new
                            isHide: false
                            isHideTab: false
                            link: 'https://www.baidu.com'
                            isIframe: false
                            authList:
                              -
                                title: 用户列表
                                authMark: 'user:list'
                            isFirstLevel: false
                            fixedTab: false
                            activePath: /user/list
                            isFullPage: false
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 1
                        parent_id: 0
                        name: User
                        path: /user
                        component: User
                        title: 用户管理
                        icon: user
                        label: user
                        sort: 1
                        is_hide: false
                        is_hide_tab: false
                        link: 'https://www.baidu.com'
                        is_iframe: false
                        keep_alive: true
                        is_first_level: false
                        fixed_tab: false
                        active_path: /user
                        is_full_page: false
                        show_badge: false
                        show_text_badge: new
                        status: true
                        meta:
                          title: 用户管理
                          icon: user
                          keepAlive: true
                          showBadge: false
                          showTextBadge: new
                          isHide: false
                          isHideTab: false
                          link: 'https://www.baidu.com'
                          isIframe: false
                          authList:
                            -
                              title: 用户列表
                              authMark: 'user:list'
                          isFirstLevel: false
                          fixedTab: false
                          activePath: /user
                          isFullPage: false
                        children:
                          -
                            id: 2
                            parent_id: 1
                            name: UserList
                            path: /user/list
                            component: UserList
                            title: 用户列表
                            icon: user
                            label: 'user:list'
                            sort: 1
                            is_hide: false
                            is_hide_tab: false
                            link: 'https://www.baidu.com'
                            is_iframe: false
                            keep_alive: true
                            is_first_level: false
                            fixed_tab: false
                            active_path: /user/list
                            is_full_page: false
                            show_badge: false
                            show_text_badge: new
                            status: true
                            meta:
                              title: 用户列表
                              icon: user
                              keepAlive: true
                              showBadge: false
                              showTextBadge: new
                              isHide: false
                              isHideTab: false
                              link: 'https://www.baidu.com'
                              isIframe: false
                              authList:
                                -
                                  title: 用户列表
                                  authMark: 'user:list'
                              isFirstLevel: false
                              fixedTab: false
                              activePath: /user/list
                              isFullPage: false
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        parent_id:
                          type: integer
                          example: 0
                        name:
                          type: string
                          example: User
                        path:
                          type: string
                          example: /user
                        component:
                          type: string
                          example: User
                        title:
                          type: string
                          example: 用户管理
                        icon:
                          type: string
                          example: user
                        label:
                          type: string
                          example: user
                        sort:
                          type: integer
                          example: 1
                        is_hide:
                          type: boolean
                          example: false
                        is_hide_tab:
                          type: boolean
                          example: false
                        link:
                          type: string
                          example: 'https://www.baidu.com'
                        is_iframe:
                          type: boolean
                          example: false
                        keep_alive:
                          type: boolean
                          example: true
                        is_first_level:
                          type: boolean
                          example: false
                        fixed_tab:
                          type: boolean
                          example: false
                        active_path:
                          type: string
                          example: /user
                        is_full_page:
                          type: boolean
                          example: false
                        show_badge:
                          type: boolean
                          example: false
                        show_text_badge:
                          type: string
                          example: new
                        status:
                          type: boolean
                          example: true
                        meta:
                          type: object
                          properties:
                            title:
                              type: string
                              example: 用户管理
                            icon:
                              type: string
                              example: user
                            keepAlive:
                              type: boolean
                              example: true
                            showBadge:
                              type: boolean
                              example: false
                            showTextBadge:
                              type: string
                              example: new
                            isHide:
                              type: boolean
                              example: false
                            isHideTab:
                              type: boolean
                              example: false
                            link:
                              type: string
                              example: 'https://www.baidu.com'
                            isIframe:
                              type: boolean
                              example: false
                            authList:
                              type: array
                              example:
                                -
                                  title: 用户列表
                                  authMark: 'user:list'
                              items:
                                type: object
                                properties:
                                  title:
                                    type: string
                                    example: 用户列表
                                  authMark:
                                    type: string
                                    example: 'user:list'
                            isFirstLevel:
                              type: boolean
                              example: false
                            fixedTab:
                              type: boolean
                              example: false
                            activePath:
                              type: string
                              example: /user
                            isFullPage:
                              type: boolean
                              example: false
                        children:
                          type: array
                          example:
                            -
                              id: 2
                              parent_id: 1
                              name: UserList
                              path: /user/list
                              component: UserList
                              title: 用户列表
                              icon: user
                              label: 'user:list'
                              sort: 1
                              is_hide: false
                              is_hide_tab: false
                              link: 'https://www.baidu.com'
                              is_iframe: false
                              keep_alive: true
                              is_first_level: false
                              fixed_tab: false
                              active_path: /user/list
                              is_full_page: false
                              show_badge: false
                              show_text_badge: new
                              status: true
                              meta:
                                title: 用户列表
                                icon: user
                                keepAlive: true
                                showBadge: false
                                showTextBadge: new
                                isHide: false
                                isHideTab: false
                                link: 'https://www.baidu.com'
                                isIframe: false
                                authList:
                                  -
                                    title: 用户列表
                                    authMark: 'user:list'
                                isFirstLevel: false
                                fixedTab: false
                                activePath: /user/list
                                isFullPage: false
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 2
                              parent_id:
                                type: integer
                                example: 1
                              name:
                                type: string
                                example: UserList
                              path:
                                type: string
                                example: /user/list
                              component:
                                type: string
                                example: UserList
                              title:
                                type: string
                                example: 用户列表
                              icon:
                                type: string
                                example: user
                              label:
                                type: string
                                example: 'user:list'
                              sort:
                                type: integer
                                example: 1
                              is_hide:
                                type: boolean
                                example: false
                              is_hide_tab:
                                type: boolean
                                example: false
                              link:
                                type: string
                                example: 'https://www.baidu.com'
                              is_iframe:
                                type: boolean
                                example: false
                              keep_alive:
                                type: boolean
                                example: true
                              is_first_level:
                                type: boolean
                                example: false
                              fixed_tab:
                                type: boolean
                                example: false
                              active_path:
                                type: string
                                example: /user/list
                              is_full_page:
                                type: boolean
                                example: false
                              show_badge:
                                type: boolean
                                example: false
                              show_text_badge:
                                type: string
                                example: new
                              status:
                                type: boolean
                                example: true
                              meta:
                                type: object
                                properties:
                                  title:
                                    type: string
                                    example: 用户列表
                                  icon:
                                    type: string
                                    example: user
                                  keepAlive:
                                    type: boolean
                                    example: true
                                  showBadge:
                                    type: boolean
                                    example: false
                                  showTextBadge:
                                    type: string
                                    example: new
                                  isHide:
                                    type: boolean
                                    example: false
                                  isHideTab:
                                    type: boolean
                                    example: false
                                  link:
                                    type: string
                                    example: 'https://www.baidu.com'
                                  isIframe:
                                    type: boolean
                                    example: false
                                  authList:
                                    type: array
                                    example:
                                      - { title: 用户列表, authMark: 'user:list' }
                                    items:
                                      type: object
                                      properties: { title: { type: string, example: 用户列表 }, authMark: { type: string, example: 'user:list' } }
                                  isFirstLevel:
                                    type: boolean
                                    example: false
                                  fixedTab:
                                    type: boolean
                                    example: false
                                  activePath:
                                    type: string
                                    example: /user/list
                                  isFullPage:
                                    type: boolean
                                    example: false
      tags:
        - 菜单管理
  '/api/admin/menus/{id}':
    put:
      summary: 更新菜单
      operationId: ''
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    id: 1
                    parent_id: 0
                    name: User
                    path: /user
                    component: User
                    title: 用户管理
                    icon: user
                    label: user
                    sort: 1
                    is_hide: false
                    is_hide_tab: false
                    link: 'https://www.baidu.com'
                    is_iframe: false
                    keep_alive: true
                    is_first_level: false
                    fixed_tab: false
                    active_path: /user
                    is_full_page: false
                    show_badge: false
                    show_text_badge: new
                    status: true
                    meta:
                      title: 用户管理
                      icon: user
                      keepAlive: true
                      showBadge: false
                      showTextBadge: new
                      isHide: false
                      isHideTab: false
                      link: 'https://www.baidu.com'
                      isIframe: false
                      authList:
                        -
                          title: 用户列表
                          authMark: 'user:list'
                      isFirstLevel: false
                      fixedTab: false
                      activePath: /user
                      isFullPage: false
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      parent_id:
                        type: integer
                        example: 0
                      name:
                        type: string
                        example: User
                      path:
                        type: string
                        example: /user
                      component:
                        type: string
                        example: User
                      title:
                        type: string
                        example: 用户管理
                      icon:
                        type: string
                        example: user
                      label:
                        type: string
                        example: user
                      sort:
                        type: integer
                        example: 1
                      is_hide:
                        type: boolean
                        example: false
                      is_hide_tab:
                        type: boolean
                        example: false
                      link:
                        type: string
                        example: 'https://www.baidu.com'
                      is_iframe:
                        type: boolean
                        example: false
                      keep_alive:
                        type: boolean
                        example: true
                      is_first_level:
                        type: boolean
                        example: false
                      fixed_tab:
                        type: boolean
                        example: false
                      active_path:
                        type: string
                        example: /user
                      is_full_page:
                        type: boolean
                        example: false
                      show_badge:
                        type: boolean
                        example: false
                      show_text_badge:
                        type: string
                        example: new
                      status:
                        type: boolean
                        example: true
                      meta:
                        type: object
                        properties:
                          title:
                            type: string
                            example: 用户管理
                          icon:
                            type: string
                            example: user
                          keepAlive:
                            type: boolean
                            example: true
                          showBadge:
                            type: boolean
                            example: false
                          showTextBadge:
                            type: string
                            example: new
                          isHide:
                            type: boolean
                            example: false
                          isHideTab:
                            type: boolean
                            example: false
                          link:
                            type: string
                            example: 'https://www.baidu.com'
                          isIframe:
                            type: boolean
                            example: false
                          authList:
                            type: array
                            example:
                              -
                                title: 用户列表
                                authMark: 'user:list'
                            items:
                              type: object
                              properties:
                                title:
                                  type: string
                                  example: 用户列表
                                authMark:
                                  type: string
                                  example: 'user:list'
                          isFirstLevel:
                            type: boolean
                            example: false
                          fixedTab:
                            type: boolean
                            example: false
                          activePath:
                            type: string
                            example: /user
                          isFullPage:
                            type: boolean
                            example: false
      tags:
        - 菜单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                parent_id:
                  type: integer
                  description: 父级菜单ID
                  example: 1
                  nullable: true
                name:
                  type: string
                  description: 菜单名称
                  example: 用户管理
                  nullable: true
                path:
                  type: string
                  description: 菜单路径
                  example: /user
                  nullable: true
                component:
                  type: string
                  description: 组件路径
                  example: User
                  nullable: true
                title:
                  type: string
                  description: 菜单标题
                  example: 用户管理
                  nullable: false
                icon:
                  type: string
                  description: 菜单图标
                  example: user
                  nullable: true
                label:
                  type: string
                  description: 权限标识
                  example: user
                  nullable: true
                sort:
                  type: integer
                  description: 排序
                  example: 1
                  nullable: false
                is_hide:
                  type: boolean
                  description: 是否隐藏
                  example: false
                  nullable: false
                is_hide_tab:
                  type: boolean
                  description: 是否在标签页隐藏
                  example: false
                  nullable: false
                link:
                  type: string
                  description: 外部链接
                  example: 'https://www.baidu.com'
                  nullable: true
                is_iframe:
                  type: boolean
                  description: 是否为iframe
                  example: false
                  nullable: false
                keep_alive:
                  type: boolean
                  description: 是否缓存
                  example: true
                  nullable: false
                is_first_level:
                  type: boolean
                  description: 是否为一级菜单
                  example: false
                  nullable: false
                fixed_tab:
                  type: boolean
                  description: 是否固定标签页
                  example: false
                  nullable: false
                active_path:
                  type: string
                  description: 激活菜单路径
                  example: /user
                  nullable: true
                is_full_page:
                  type: boolean
                  description: 是否全屏页面
                  example: false
                  nullable: false
                show_badge:
                  type: boolean
                  description: 是否显示徽章
                  example: false
                  nullable: false
                show_text_badge:
                  type: string
                  description: 文本徽章内容
                  example: new
                  nullable: true
                status:
                  type: boolean
                  description: 状态
                  example: true
                  nullable: false
                permissions:
                  type: array
                  description: 权限列表
                  example:
                    -
                      title: 用户列表
                      auth_mark: 'user:list'
                      sort: 1
                  items:
                    type: string
              required:
                - name
                - path
                - title
    delete:
      summary: 删除菜单
      operationId: ''
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: 菜单删除成功
                properties:
                  message:
                    type: string
                    example: 菜单删除成功
      tags:
        - 菜单管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the menu.'
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/roles:
    get:
      summary: 获取角色列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: search
          description: 搜索关键词（角色名称）.
          example: admin
          required: false
          schema:
            type: string
            description: 搜索关键词（角色名称）.
            example: admin
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数.
          example: 20
          required: false
          schema:
            type: integer
            description: 每页条数.
            example: 20
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 1
                      name: admin
                      guard_name: null
                      description: 系统管理员
                      menus:
                        -
                          menu_id: 1
                          permission_ids: []
                        -
                          menu_id: 2
                          permission_ids: []
                        -
                          menu_id: 3
                          permission_ids: []
                        -
                          menu_id: 4
                          permission_ids:
                            - 1
                            - 2
                            - 3
                            - 4
                            - 5
                        -
                          menu_id: 5
                          permission_ids:
                            - 9
                            - 6
                            - 7
                            - 8
                        -
                          menu_id: 6
                          permission_ids: []
                        -
                          menu_id: 7
                          permission_ids:
                            - 10
                            - 11
                            - 12
                        -
                          menu_id: 8
                          permission_ids:
                            - 13
                            - 14
                        -
                          menu_id: 9
                          permission_ids: []
                        -
                          menu_id: 10
                          permission_ids: []
                        -
                          menu_id: 11
                          permission_ids:
                            - 15
                            - 16
                            - 17
                        -
                          menu_id: 12
                          permission_ids:
                            - 18
                            - 19
                            - 20
                        -
                          menu_id: 13
                          permission_ids:
                            - 21
                            - 22
                            - 23
                        -
                          menu_id: 14
                          permission_ids:
                            - 24
                            - 25
                            - 26
                        -
                          menu_id: 15
                          permission_ids:
                            - 30
                            - 27
                            - 28
                            - 29
                        -
                          menu_id: 16
                          permission_ids:
                            - 31
                            - 32
                        -
                          menu_id: 17
                          permission_ids:
                            - 33
                            - 34
                            - 35
                        -
                          menu_id: 18
                          permission_ids:
                            - 36
                            - 37
                            - 38
                            - 39
                      created_at: '2025-08-22T08:33:20.000000Z'
                      updated_at: '2025-08-22T08:33:20.000000Z'
                    -
                      id: 1
                      name: admin
                      guard_name: null
                      description: 系统管理员
                      menus:
                        -
                          menu_id: 1
                          permission_ids: []
                        -
                          menu_id: 2
                          permission_ids: []
                        -
                          menu_id: 3
                          permission_ids: []
                        -
                          menu_id: 4
                          permission_ids:
                            - 1
                            - 2
                            - 3
                            - 4
                            - 5
                        -
                          menu_id: 5
                          permission_ids:
                            - 9
                            - 6
                            - 7
                            - 8
                        -
                          menu_id: 6
                          permission_ids: []
                        -
                          menu_id: 7
                          permission_ids:
                            - 10
                            - 11
                            - 12
                        -
                          menu_id: 8
                          permission_ids:
                            - 13
                            - 14
                        -
                          menu_id: 9
                          permission_ids: []
                        -
                          menu_id: 10
                          permission_ids: []
                        -
                          menu_id: 11
                          permission_ids:
                            - 15
                            - 16
                            - 17
                        -
                          menu_id: 12
                          permission_ids:
                            - 18
                            - 19
                            - 20
                        -
                          menu_id: 13
                          permission_ids:
                            - 21
                            - 22
                            - 23
                        -
                          menu_id: 14
                          permission_ids:
                            - 24
                            - 25
                            - 26
                        -
                          menu_id: 15
                          permission_ids:
                            - 30
                            - 27
                            - 28
                            - 29
                        -
                          menu_id: 16
                          permission_ids:
                            - 31
                            - 32
                        -
                          menu_id: 17
                          permission_ids:
                            - 33
                            - 34
                            - 35
                        -
                          menu_id: 18
                          permission_ids:
                            - 36
                            - 37
                            - 38
                            - 39
                      created_at: '2025-08-22T08:33:20.000000Z'
                      updated_at: '2025-08-22T08:33:20.000000Z'
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 1
                        name: admin
                        guard_name: null
                        description: 系统管理员
                        menus:
                          -
                            menu_id: 1
                            permission_ids: []
                          -
                            menu_id: 2
                            permission_ids: []
                          -
                            menu_id: 3
                            permission_ids: []
                          -
                            menu_id: 4
                            permission_ids:
                              - 1
                              - 2
                              - 3
                              - 4
                              - 5
                          -
                            menu_id: 5
                            permission_ids:
                              - 9
                              - 6
                              - 7
                              - 8
                          -
                            menu_id: 6
                            permission_ids: []
                          -
                            menu_id: 7
                            permission_ids:
                              - 10
                              - 11
                              - 12
                          -
                            menu_id: 8
                            permission_ids:
                              - 13
                              - 14
                          -
                            menu_id: 9
                            permission_ids: []
                          -
                            menu_id: 10
                            permission_ids: []
                          -
                            menu_id: 11
                            permission_ids:
                              - 15
                              - 16
                              - 17
                          -
                            menu_id: 12
                            permission_ids:
                              - 18
                              - 19
                              - 20
                          -
                            menu_id: 13
                            permission_ids:
                              - 21
                              - 22
                              - 23
                          -
                            menu_id: 14
                            permission_ids:
                              - 24
                              - 25
                              - 26
                          -
                            menu_id: 15
                            permission_ids:
                              - 30
                              - 27
                              - 28
                              - 29
                          -
                            menu_id: 16
                            permission_ids:
                              - 31
                              - 32
                          -
                            menu_id: 17
                            permission_ids:
                              - 33
                              - 34
                              - 35
                          -
                            menu_id: 18
                            permission_ids:
                              - 36
                              - 37
                              - 38
                              - 39
                        created_at: '2025-08-22T08:33:20.000000Z'
                        updated_at: '2025-08-22T08:33:20.000000Z'
                      -
                        id: 1
                        name: admin
                        guard_name: null
                        description: 系统管理员
                        menus:
                          -
                            menu_id: 1
                            permission_ids: []
                          -
                            menu_id: 2
                            permission_ids: []
                          -
                            menu_id: 3
                            permission_ids: []
                          -
                            menu_id: 4
                            permission_ids:
                              - 1
                              - 2
                              - 3
                              - 4
                              - 5
                          -
                            menu_id: 5
                            permission_ids:
                              - 9
                              - 6
                              - 7
                              - 8
                          -
                            menu_id: 6
                            permission_ids: []
                          -
                            menu_id: 7
                            permission_ids:
                              - 10
                              - 11
                              - 12
                          -
                            menu_id: 8
                            permission_ids:
                              - 13
                              - 14
                          -
                            menu_id: 9
                            permission_ids: []
                          -
                            menu_id: 10
                            permission_ids: []
                          -
                            menu_id: 11
                            permission_ids:
                              - 15
                              - 16
                              - 17
                          -
                            menu_id: 12
                            permission_ids:
                              - 18
                              - 19
                              - 20
                          -
                            menu_id: 13
                            permission_ids:
                              - 21
                              - 22
                              - 23
                          -
                            menu_id: 14
                            permission_ids:
                              - 24
                              - 25
                              - 26
                          -
                            menu_id: 15
                            permission_ids:
                              - 30
                              - 27
                              - 28
                              - 29
                          -
                            menu_id: 16
                            permission_ids:
                              - 31
                              - 32
                          -
                            menu_id: 17
                            permission_ids:
                              - 33
                              - 34
                              - 35
                          -
                            menu_id: 18
                            permission_ids:
                              - 36
                              - 37
                              - 38
                              - 39
                        created_at: '2025-08-22T08:33:20.000000Z'
                        updated_at: '2025-08-22T08:33:20.000000Z'
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        name:
                          type: string
                          example: admin
                        guard_name:
                          type: string
                          example: null
                        description:
                          type: string
                          example: 系统管理员
                        menus:
                          type: array
                          example:
                            -
                              menu_id: 1
                              permission_ids: []
                            -
                              menu_id: 2
                              permission_ids: []
                            -
                              menu_id: 3
                              permission_ids: []
                            -
                              menu_id: 4
                              permission_ids:
                                - 1
                                - 2
                                - 3
                                - 4
                                - 5
                            -
                              menu_id: 5
                              permission_ids:
                                - 9
                                - 6
                                - 7
                                - 8
                            -
                              menu_id: 6
                              permission_ids: []
                            -
                              menu_id: 7
                              permission_ids:
                                - 10
                                - 11
                                - 12
                            -
                              menu_id: 8
                              permission_ids:
                                - 13
                                - 14
                            -
                              menu_id: 9
                              permission_ids: []
                            -
                              menu_id: 10
                              permission_ids: []
                            -
                              menu_id: 11
                              permission_ids:
                                - 15
                                - 16
                                - 17
                            -
                              menu_id: 12
                              permission_ids:
                                - 18
                                - 19
                                - 20
                            -
                              menu_id: 13
                              permission_ids:
                                - 21
                                - 22
                                - 23
                            -
                              menu_id: 14
                              permission_ids:
                                - 24
                                - 25
                                - 26
                            -
                              menu_id: 15
                              permission_ids:
                                - 30
                                - 27
                                - 28
                                - 29
                            -
                              menu_id: 16
                              permission_ids:
                                - 31
                                - 32
                            -
                              menu_id: 17
                              permission_ids:
                                - 33
                                - 34
                                - 35
                            -
                              menu_id: 18
                              permission_ids:
                                - 36
                                - 37
                                - 38
                                - 39
                          items:
                            type: object
                            properties:
                              menu_id:
                                type: integer
                                example: 1
                              permission_ids:
                                type: array
                                example: []
                        created_at:
                          type: string
                          example: '2025-08-22T08:33:20.000000Z'
                        updated_at:
                          type: string
                          example: '2025-08-22T08:33:20.000000Z'
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 角色管理
    post:
      summary: 创建角色
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 角色管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 角色名称.
                  example: 管理员
                  nullable: false
                description:
                  type: string
                  description: 角色描述.
                  example: 系统管理员角色
                  nullable: true
              required:
                - name
  '/api/admin/roles/{id}':
    get:
      summary: 获取角色详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 角色管理
    put:
      summary: 更新角色
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 角色管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 角色名称.
                  example: 管理员
                  nullable: false
                description:
                  type: string
                  description: 角色描述.
                  example: 系统管理员角色
                  nullable: true
              required:
                - name
    delete:
      summary: 删除角色
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 角色管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the role.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: role
        description: 角色ID.
        example: 1
        required: true
        schema:
          type: integer
  '/api/admin/roles/{role_id}/menu-permissions/assign':
    post:
      summary: 分配菜单权限
      operationId: ''
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: 权限分配成功
                properties:
                  message:
                    type: string
                    example: 权限分配成功
      tags:
        - 角色管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                permissions:
                  type: array
                  description: 权限数组.
                  example:
                    -
                      menu_id: 1
                      permission_ids:
                        - 1
                        - 2
                        - 3
                        - 4
                        - 5
                    -
                      menu_id: 1
                      permission_ids:
                        - 1
                        - 2
                        - 3
                        - 4
                        - 5
                  items:
                    type: string
              required:
                - permissions
    parameters:
      -
        in: path
        name: role_id
        description: 'The ID of the role.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: role
        description: 角色ID.
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/assets:
    get:
      summary: 获取资产列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: name
          description: 资产名称搜索
          example: 办公电脑
          required: false
          schema:
            type: string
            description: 资产名称搜索
            example: 办公电脑
            nullable: false
        -
          in: query
          name: brand_id
          description: 品牌ID搜索
          example: 1
          required: false
          schema:
            type: integer
            description: 品牌ID搜索
            example: 1
            nullable: false
        -
          in: query
          name: serial_number
          description: 序列号搜索
          example: ABC123456
          required: false
          schema:
            type: string
            description: 序列号搜索
            example: ABC123456
            nullable: false
        -
          in: query
          name: keyword
          description: 通用搜索关键词（同时搜索名称、品牌、型号、序列号）
          example: 联想
          required: false
          schema:
            type: string
            description: 通用搜索关键词（同时搜索名称、品牌、型号、序列号）
            example: 联想
            nullable: false
        -
          in: query
          name: asset_category_id
          description: 资产分类ID
          example: 1
          required: false
          schema:
            type: integer
            description: 资产分类ID
            example: 1
            nullable: false
        -
          in: query
          name: department_category_id
          description: 科室分类ID
          example: 2
          required: false
          schema:
            type: integer
            description: 科室分类ID
            example: 2
            nullable: false
        -
          in: query
          name: industry_category_id
          description: 行业分类ID
          example: 3
          required: false
          schema:
            type: integer
            description: 行业分类ID
            example: 3
            nullable: false
        -
          in: query
          name: asset_status
          description: 资产状态（字典code）
          example: in_use
          required: false
          schema:
            type: string
            description: 资产状态（字典code）
            example: in_use
            nullable: false
        -
          in: query
          name: asset_condition
          description: 成色（字典code）
          example: brand_new
          required: false
          schema:
            type: string
            description: 成色（字典code）
            example: brand_new
            nullable: false
        -
          in: query
          name: asset_source
          description: 资产来源（字典code）
          example: purchase
          required: false
          schema:
            type: string
            description: 资产来源（字典code）
            example: purchase
            nullable: false
        -
          in: query
          name: is_accessory
          description: 是否附属设备
          example: false
          required: false
          schema:
            type: boolean
            description: 是否附属设备
            example: false
            nullable: false
        -
          in: query
          name: parent_id
          description: 主设备ID
          example: 1
          required: false
          schema:
            type: integer
            description: 主设备ID
            example: 1
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 20
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 20
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: 51
                      name: 生化分析仪
                      brand_id: 65
                      model: DG4-944
                      serial_number: 665a39c0-48af-31f1-a546-aa4f41372488
                      asset_category_ids:
                        - 1
                        - 2
                      asset_category:
                        -
                          id: 1
                          name: 行业分类
                          code: asset_industry_category
                        -
                          id: 2
                          name: 科室
                          code: asset_department
                      asset_source: purchase
                      asset_status: scrap_registered
                      asset_condition: brand_new
                      parent_id: null
                      children_count: 0
                      region_code: '330105000000'
                      detailed_address: '4351 Keely Wells'
                      start_date: 1584087043
                      warranty_period: 45
                      warranty_alert: 56
                      maintenance_cycle: 138
                      expected_years: 5
                      related_entities: null
                      remark: 'Molestias fugit deleniti distinctio eum doloremque id.'
                      created_by: 1
                      updated_by: 1
                      created_at: 1755852649
                      updated_at: 1755852649
                    -
                      id: 52
                      name: 骨密度仪
                      brand_id: 66
                      model: 1V91S-321
                      serial_number: 84614a84-7a78-3276-b72a-735334c9a032
                      asset_category_ids:
                        - 1
                        - 2
                      asset_category:
                        -
                          id: 1
                          name: 行业分类
                          code: asset_industry_category
                        -
                          id: 2
                          name: 科室
                          code: asset_department
                      asset_source: produce
                      asset_status: pending_check
                      asset_condition: brand_new
                      parent_id: null
                      children_count: 0
                      region_code: '441622000000'
                      detailed_address: '4161 Bauch Loaf Suite 045'
                      start_date: 1416079706
                      warranty_period: 36
                      warranty_alert: 68
                      maintenance_cycle: 63
                      expected_years: 9
                      related_entities: null
                      remark: 'Ut dicta vitae assumenda consequatur ut et sunt.'
                      created_by: 1
                      updated_by: 1
                      created_at: 1755852649
                      updated_at: 1755852649
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: 51
                        name: 生化分析仪
                        brand_id: 65
                        model: DG4-944
                        serial_number: 665a39c0-48af-31f1-a546-aa4f41372488
                        asset_category_ids:
                          - 1
                          - 2
                        asset_category:
                          -
                            id: 1
                            name: 行业分类
                            code: asset_industry_category
                          -
                            id: 2
                            name: 科室
                            code: asset_department
                        asset_source: purchase
                        asset_status: scrap_registered
                        asset_condition: brand_new
                        parent_id: null
                        children_count: 0
                        region_code: '330105000000'
                        detailed_address: '4351 Keely Wells'
                        start_date: 1584087043
                        warranty_period: 45
                        warranty_alert: 56
                        maintenance_cycle: 138
                        expected_years: 5
                        related_entities: null
                        remark: 'Molestias fugit deleniti distinctio eum doloremque id.'
                        created_by: 1
                        updated_by: 1
                        created_at: 1755852649
                        updated_at: 1755852649
                      -
                        id: 52
                        name: 骨密度仪
                        brand_id: 66
                        model: 1V91S-321
                        serial_number: 84614a84-7a78-3276-b72a-735334c9a032
                        asset_category_ids:
                          - 1
                          - 2
                        asset_category:
                          -
                            id: 1
                            name: 行业分类
                            code: asset_industry_category
                          -
                            id: 2
                            name: 科室
                            code: asset_department
                        asset_source: produce
                        asset_status: pending_check
                        asset_condition: brand_new
                        parent_id: null
                        children_count: 0
                        region_code: '441622000000'
                        detailed_address: '4161 Bauch Loaf Suite 045'
                        start_date: 1416079706
                        warranty_period: 36
                        warranty_alert: 68
                        maintenance_cycle: 63
                        expected_years: 9
                        related_entities: null
                        remark: 'Ut dicta vitae assumenda consequatur ut et sunt.'
                        created_by: 1
                        updated_by: 1
                        created_at: 1755852649
                        updated_at: 1755852649
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 51
                        name:
                          type: string
                          example: 生化分析仪
                        brand_id:
                          type: integer
                          example: 65
                        model:
                          type: string
                          example: DG4-944
                        serial_number:
                          type: string
                          example: 665a39c0-48af-31f1-a546-aa4f41372488
                        asset_category_ids:
                          type: array
                          example:
                            - 1
                            - 2
                          items:
                            type: integer
                        asset_category:
                          type: array
                          example:
                            -
                              id: 1
                              name: 行业分类
                              code: asset_industry_category
                            -
                              id: 2
                              name: 科室
                              code: asset_department
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 1
                              name:
                                type: string
                                example: 行业分类
                              code:
                                type: string
                                example: asset_industry_category
                        asset_source:
                          type: string
                          example: purchase
                        asset_status:
                          type: string
                          example: scrap_registered
                        asset_condition:
                          type: string
                          example: brand_new
                        parent_id:
                          type: string
                          example: null
                        children_count:
                          type: integer
                          example: 0
                        region_code:
                          type: string
                          example: '330105000000'
                        detailed_address:
                          type: string
                          example: '4351 Keely Wells'
                        start_date:
                          type: integer
                          example: 1584087043
                        warranty_period:
                          type: integer
                          example: 45
                        warranty_alert:
                          type: integer
                          example: 56
                        maintenance_cycle:
                          type: integer
                          example: 138
                        expected_years:
                          type: integer
                          example: 5
                        related_entities:
                          type: string
                          example: null
                        remark:
                          type: string
                          example: 'Molestias fugit deleniti distinctio eum doloremque id.'
                        created_by:
                          type: integer
                          example: 1
                        updated_by:
                          type: integer
                          example: 1
                        created_at:
                          type: integer
                          example: 1755852649
                        updated_at:
                          type: integer
                          example: 1755852649
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 资产管理
    post:
      summary: 创建资产
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 资产名称
                  example: 办公台式电脑
                  nullable: false
                brand_id:
                  type: integer
                  description: 品牌ID
                  example: 1
                  nullable: true
                model:
                  type: string
                  description: 规格型号
                  example: 'ThinkCentre M720'
                  nullable: true
                serial_number:
                  type: string
                  description: 序列号
                  example: ABC123456789
                  nullable: true
                asset_category_ids:
                  type: array
                  description: 资产分类ID
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
                asset_source:
                  type: string
                  description: 资产来源（字典code）
                  example: purchase
                  nullable: true
                asset_status:
                  type: string
                  description: 资产状态（字典code）
                  example: new_unstocked
                  nullable: true
                asset_condition:
                  type: string
                  description: 成色（字典code）
                  example: brand_new
                  nullable: true
                parent_id:
                  type: integer
                  description: 主设备ID（附属设备时必填）
                  example: 1
                  nullable: true
                region_code:
                  type: string
                  description: 地区代码
                  example: '12'
                  nullable: true
                detailed_address:
                  type: string
                  description: 详细地址
                  example: XX街道XX号XX大厦
                  nullable: true
                start_date:
                  type: date
                  description: 启用日期
                  example: '2024-01-01'
                  nullable: true
                warranty_period:
                  type: integer
                  description: 合同质保期（月）
                  example: 36
                  nullable: true
                warranty_alert:
                  type: integer
                  description: 质保期预警（天）
                  example: 30
                  nullable: true
                maintenance_cycle:
                  type: integer
                  description: 维护周期（天）
                  example: 90
                  nullable: true
                expected_years:
                  type: integer
                  description: 预计使用年限（年）
                  example: 5
                  nullable: true
                related_entities:
                  type: array
                  description: '相关方信息 JSON数组'
                  example:
                    -
                      entity_type: manufacturer
                      entity_id: 1
                      contact_name: 张三
                      contact_phone: '***********'
                      position: 产品经理
                      department: 产品部
                  items:
                    type: string
                    nullable: true
                remark:
                  type: string
                  description: 备注
                  example: architecto
                  nullable: true
                attachments:
                  type: array
                  description: 附件ID数组
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
                is_accessory:
                  type: boolean
                  description: 是否附属设备
                  example: false
                  nullable: false
              required:
                - name
  /api/admin/assets/main-assets:
    get:
      summary: 获取可作为主设备的资产列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: exclude_id
          description: 排除的资产ID（避免自己关联自己）
          example: 5
          required: false
          schema:
            type: integer
            description: 排除的资产ID（避免自己关联自己）
            example: 5
            nullable: false
        -
          in: query
          name: keyword
          description: 搜索关键词
          example: 电脑
          required: false
          schema:
            type: string
            description: 搜索关键词
            example: 电脑
            nullable: false
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页条数
          example: 20
          required: false
          schema:
            type: integer
            description: 每页条数
            example: 20
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
  /api/admin/assets/export-template:
    get:
      summary: 导出资产模板
      operationId: ''
      description: 导出包含所有字段的Excel模板文件，用于资产批量导入
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
  '/api/admin/assets/{id}':
    get:
      summary: 获取资产详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
    put:
      summary: 更新资产
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 资产名称
                  example: 办公台式电脑
                  nullable: false
                brand_id:
                  type: integer
                  description: 品牌ID
                  example: 1
                  nullable: true
                model:
                  type: string
                  description: 规格型号
                  example: 'ThinkCentre M720'
                  nullable: true
                serial_number:
                  type: string
                  description: 序列号
                  example: ABC123456789
                  nullable: true
                asset_category_ids:
                  type: array
                  description: 资产分类ID
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
                asset_source:
                  type: string
                  description: 资产来源（字典code）
                  example: purchase
                  nullable: true
                asset_status:
                  type: string
                  description: 资产状态（字典code）
                  example: new_unstocked
                  nullable: true
                asset_condition:
                  type: string
                  description: 成色（字典code）
                  example: brand_new
                  nullable: true
                parent_id:
                  type: integer
                  description: 主设备ID（附属设备时必填）
                  example: 1
                  nullable: true
                region_code:
                  type: string
                  description: 地区代码
                  example: '12'
                  nullable: true
                detailed_address:
                  type: string
                  description: 详细地址
                  example: XX街道XX号XX大厦
                  nullable: true
                start_date:
                  type: date
                  description: 启用日期
                  example: '2024-01-01'
                  nullable: true
                warranty_period:
                  type: integer
                  description: 合同质保期（月）
                  example: 36
                  nullable: true
                warranty_alert:
                  type: integer
                  description: 质保期预警（天）
                  example: 30
                  nullable: true
                maintenance_cycle:
                  type: integer
                  description: 维护周期（天）
                  example: 90
                  nullable: true
                expected_years:
                  type: integer
                  description: 预计使用年限（年）
                  example: 5
                  nullable: true
                related_entities:
                  type: array
                  description: '相关方信息 JSON数组'
                  example:
                    -
                      entity_type: manufacturer
                      entity_id: 1
                      contact_name: 张三
                      contact_phone: '***********'
                      position: 产品经理
                      department: 产品部
                  items:
                    type: string
                    nullable: true
                remark:
                  type: string
                  description: 备注
                  example: architecto
                  nullable: true
                attachments:
                  type: array
                  description: 附件ID数组
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
                is_accessory:
                  type: boolean
                  description: 是否附属设备
                  example: false
                  nullable: false
              required:
                - name
    delete:
      summary: 删除资产
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the asset.'
        example: 1
        required: true
        schema:
          type: integer
      -
        in: path
        name: asset
        description: 资产ID
        example: 1
        required: true
        schema:
          type: integer
  /api/admin/assets/batch/copy:
    post:
      summary: 批量复制资产
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ids:
                  type: array
                  description: 资产ID数组
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
              required:
                - ids
  /api/admin/assets/batch/destroy:
    post:
      summary: 批量删除资产
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 资产管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ids:
                  type: array
                  description: 资产ID数组
                  example:
                    - 1
                    - 2
                    - 3
                  items:
                    type: string
              required:
                - ids
  /api/admin/configs:
    get:
      summary: 获取所有配置
      operationId: ''
      description: 获取系统配置和上传配置
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  system:
                    system_name: 设备云管理系统
                    system_logo: 'https://example.com/logo.png'
                  upload:
                    storage_type: alioss|qiniu|local
                    aliyun_access_key: '1234567890'
                    aliyun_access_secret: '1234567890'
                    aliyun_bucket: tc-kyx
                    aliyun_region: oss-cn-hangzhou
                    aliyun_endpoint: oss-cn-hangzhou.aliyuncs.com
                properties:
                  system:
                    type: object
                    properties:
                      system_name:
                        type: string
                        example: 设备云管理系统
                      system_logo:
                        type: string
                        example: 'https://example.com/logo.png'
                  upload:
                    type: object
                    properties:
                      storage_type:
                        type: string
                        example: alioss|qiniu|local
                      aliyun_access_key:
                        type: string
                        example: '1234567890'
                      aliyun_access_secret:
                        type: string
                        example: '1234567890'
                      aliyun_bucket:
                        type: string
                        example: tc-kyx
                      aliyun_region:
                        type: string
                        example: oss-cn-hangzhou
                      aliyun_endpoint:
                        type: string
                        example: oss-cn-hangzhou.aliyuncs.com
      tags:
        - 配置管理
  /api/admin/configs/update:
    put:
      summary: 更新配置
      operationId: ''
      description: 更新系统配置和上传配置
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 配置管理
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                configs:
                  type: 配置数据
                  description: ''
                  example: '{"system": {"system_name": "设备云管理系统", "system_logo": "https://example.com/logo.png"}, "upload": {"storage_type": "alioss|qiniu|local", "aliyun_access_key": "1234567890", "aliyun_access_secret": "1234567890", "aliyun_bucket": "tc-kyx", "aliyun_region": "oss-cn-hangzhou", "aliyun_endpoint": "oss-cn-hangzhou.aliyuncs.com"}}'
                  nullable: false
  /api/admin/configs/clear-cache:
    post:
      summary: 清除配置缓存
      operationId: ''
      description: 清除所有配置缓存
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 配置管理
  /api/admin/attachments:
    get:
      summary: 获取附件列表
      operationId: ''
      description: ''
      parameters:
        -
          in: query
          name: page
          description: 页码
          example: 1
          required: false
          schema:
            type: integer
            description: 页码
            example: 1
            nullable: false
        -
          in: query
          name: per_page
          description: 每页数量
          example: 20
          required: false
          schema:
            type: integer
            description: 每页数量
            example: 20
            nullable: false
        -
          in: query
          name: file_name
          description: 文件名（模糊搜索）
          example: example.pdf
          required: false
          schema:
            type: string
            description: 文件名（模糊搜索）
            example: example.pdf
            nullable: false
        -
          in: query
          name: start_time
          description: 开始时间
          example: '2024-01-01'
          required: false
          schema:
            type: string
            description: 开始时间
            example: '2024-01-01'
            nullable: false
        -
          in: query
          name: end_time
          description: 结束时间
          example: '2024-12-31'
          required: false
          schema:
            type: string
            description: 结束时间
            example: '2024-12-31'
            nullable: false
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  data:
                    -
                      id: ''
                      file_name: null
                      file_path: null
                      file_size: null
                      mime_type: null
                      storage_type: null
                      md5_hash: null
                      file_url: ''
                      formatted_file_size: ' bytes'
                      created_at: null
                      updated_at: null
                      relation_id: null
                      attachable_type: null
                      attachable_id: null
                      category: null
                      description: null
                    -
                      id: ''
                      file_name: null
                      file_path: null
                      file_size: null
                      mime_type: null
                      storage_type: null
                      md5_hash: null
                      file_url: ''
                      formatted_file_size: ' bytes'
                      created_at: null
                      updated_at: null
                      relation_id: null
                      attachable_type: null
                      attachable_id: null
                      category: null
                      description: null
                  links:
                    first: '/?page=1'
                    last: '/?page=1'
                    prev: null
                    next: null
                  meta:
                    current_page: 1
                    from: 1
                    last_page: 1
                    links:
                      -
                        url: null
                        label: '&laquo; Previous'
                        active: false
                      -
                        url: '/?page=1'
                        label: '1'
                        active: true
                      -
                        url: null
                        label: 'Next &raquo;'
                        active: false
                    path: /
                    per_page: 20
                    to: 2
                    total: 2
                properties:
                  data:
                    type: array
                    example:
                      -
                        id: ''
                        file_name: null
                        file_path: null
                        file_size: null
                        mime_type: null
                        storage_type: null
                        md5_hash: null
                        file_url: ''
                        formatted_file_size: ' bytes'
                        created_at: null
                        updated_at: null
                        relation_id: null
                        attachable_type: null
                        attachable_id: null
                        category: null
                        description: null
                      -
                        id: ''
                        file_name: null
                        file_path: null
                        file_size: null
                        mime_type: null
                        storage_type: null
                        md5_hash: null
                        file_url: ''
                        formatted_file_size: ' bytes'
                        created_at: null
                        updated_at: null
                        relation_id: null
                        attachable_type: null
                        attachable_id: null
                        category: null
                        description: null
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: ''
                        file_name:
                          type: string
                          example: null
                        file_path:
                          type: string
                          example: null
                        file_size:
                          type: string
                          example: null
                        mime_type:
                          type: string
                          example: null
                        storage_type:
                          type: string
                          example: null
                        md5_hash:
                          type: string
                          example: null
                        file_url:
                          type: string
                          example: ''
                        formatted_file_size:
                          type: string
                          example: ' bytes'
                        created_at:
                          type: string
                          example: null
                        updated_at:
                          type: string
                          example: null
                        relation_id:
                          type: string
                          example: null
                        attachable_type:
                          type: string
                          example: null
                        attachable_id:
                          type: string
                          example: null
                        category:
                          type: string
                          example: null
                        description:
                          type: string
                          example: null
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                        example: '/?page=1'
                      last:
                        type: string
                        example: '/?page=1'
                      prev:
                        type: string
                        example: null
                      next:
                        type: string
                        example: null
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      links:
                        type: array
                        example:
                          -
                            url: null
                            label: '&laquo; Previous'
                            active: false
                          -
                            url: '/?page=1'
                            label: '1'
                            active: true
                          -
                            url: null
                            label: 'Next &raquo;'
                            active: false
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              example: null
                            label:
                              type: string
                              example: '&laquo; Previous'
                            active:
                              type: boolean
                              example: false
                      path:
                        type: string
                        example: /
                      per_page:
                        type: integer
                        example: 20
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
      tags:
        - 附件管理
  /api/admin/attachments/upload:
    post:
      summary: 上传附件（本地上传）
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 附件管理
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 要上传的文件（最大10MB）
                  nullable: false
              required:
                - file
  '/api/admin/attachments/{id}':
    get:
      summary: 获取附件详情
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 附件管理
    parameters:
      -
        in: path
        name: id
        description: 附件ID
        example: 16
        required: true
        schema:
          type: integer
  '/api/admin/attachments/{attachment}/download':
    get:
      summary: 下载附件
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 附件管理
    parameters:
      -
        in: path
        name: attachment
        description: 'The attachment.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: id
        description: 附件ID
        example: 16
        required: true
        schema:
          type: integer
  /api/admin/attachments/sts/credentials:
    post:
      summary: 获取STS临时凭证
      operationId: STS
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  quick_upload: false
                  upload_id: 550e8400-e29b-41d4-a716-446655440000
                  credentials:
                    AccessKeyId: STS.xxx
                    AccessKeySecret: xxx
                    SecurityToken: xxx
                    Expiration: '2025-08-04T12:00:00Z'
                  region: cn-hangzhou
                  bucket: my-bucket
                  endpoint: 'https://oss-cn-hangzhou.aliyuncs.com'
                  prefix: attachments/
                properties:
                  quick_upload:
                    type: boolean
                    example: false
                  upload_id:
                    type: string
                    example: 550e8400-e29b-41d4-a716-446655440000
                  credentials:
                    type: object
                    properties:
                      AccessKeyId:
                        type: string
                        example: STS.xxx
                      AccessKeySecret:
                        type: string
                        example: xxx
                      SecurityToken:
                        type: string
                        example: xxx
                      Expiration:
                        type: string
                        example: '2025-08-04T12:00:00Z'
                  region:
                    type: string
                    example: cn-hangzhou
                  bucket:
                    type: string
                    example: my-bucket
                  endpoint:
                    type: string
                    example: 'https://oss-cn-hangzhou.aliyuncs.com'
                  prefix:
                    type: string
                    example: attachments/
      tags:
        - 附件管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filename:
                  type: string
                  description: 文件名
                  example: example.pdf
                  nullable: false
                filesize:
                  type: integer
                  description: 文件大小（字节）
                  example: 1024000
                  nullable: false
                mime_type:
                  type: string
                  description: MIME类型
                  example: application/pdf
                  nullable: false
                md5_hash:
                  type: string
                  description: 文件MD5值（用于秒传）
                  example: 5d41402abc4b2a76b9719d911017c592
                  nullable: true
              required:
                - filename
                - filesize
                - mime_type
  /api/admin/attachments/sts/confirm:
    post:
      summary: 确认上传完成
      operationId: ''
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  id: ''
                  file_name: modi.png
                  file_path: uploads/5e4f00df-4238-35bd-9edc-0b98dc359c80.png
                  file_size: 5161316
                  mime_type: image/png
                  storage_type: local
                  md5_hash: null
                  file_url: 'http://localhost:8005/storage/uploads/5e4f00df-4238-35bd-9edc-0b98dc359c80.png'
                  formatted_file_size: '4.92 MB'
                  created_at: null
                  updated_at: null
                  relation_id: null
                  attachable_type: null
                  attachable_id: null
                  category: null
                  description: null
                properties:
                  id:
                    type: string
                    example: ''
                  file_name:
                    type: string
                    example: modi.png
                  file_path:
                    type: string
                    example: uploads/5e4f00df-4238-35bd-9edc-0b98dc359c80.png
                  file_size:
                    type: integer
                    example: 5161316
                  mime_type:
                    type: string
                    example: image/png
                  storage_type:
                    type: string
                    example: local
                  md5_hash:
                    type: string
                    example: null
                  file_url:
                    type: string
                    example: 'http://localhost:8005/storage/uploads/5e4f00df-4238-35bd-9edc-0b98dc359c80.png'
                  formatted_file_size:
                    type: string
                    example: '4.92 MB'
                  created_at:
                    type: string
                    example: null
                  updated_at:
                    type: string
                    example: null
                  relation_id:
                    type: string
                    example: null
                  attachable_type:
                    type: string
                    example: null
                  attachable_id:
                    type: string
                    example: null
                  category:
                    type: string
                    example: null
                  description:
                    type: string
                    example: null
      tags:
        - 附件管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                upload_id:
                  type: string
                  description: 上传ID
                  example: 550e8400-e29b-41d4-a716-446655440000
                  nullable: false
                object_key:
                  type: string
                  description: OSS对象键值
                  example: attachments/2025/08/04/xxx.pdf
                  nullable: false
                filename:
                  type: string
                  description: 文件名（可选）
                  example: example.pdf
                  nullable: true
                filesize:
                  type: integer
                  description: 文件大小（可选）
                  example: 1024000
                  nullable: true
                mime_type:
                  type: string
                  description: MIME类型（可选）
                  example: application/pdf
                  nullable: true
              required:
                - upload_id
                - object_key
  /api/admin/attachments/by-business:
    get:
      summary: 根据业务ID获取附件列表
      operationId: ID
      description: ''
      parameters:
        -
          in: query
          name: attachable_type
          description: 业务类型
          example: App\Models\Entity
          required: true
          schema:
            type: string
            description: 业务类型
            example: App\Models\Entity
            nullable: false
        -
          in: query
          name: attachable_id
          description: 业务ID
          example: 1
          required: true
          schema:
            type: integer
            description: 业务ID
            example: 1
            nullable: false
        -
          in: query
          name: category
          description: 附件分类
          example: contract
          required: false
          schema:
            type: string
            description: 附件分类
            example: contract
            nullable: false
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 附件管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                attachable_type:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                attachable_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: false
                category:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
              required:
                - attachable_type
                - attachable_id
  '/api/admin/attachments/update-by-relation/{attachmentRelation_id}':
    put:
      summary: 更新文件关联描述
      operationId: ''
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - 附件管理
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                description:
                  type: string
                  description: 描述
                  example: 这是我的描述
                  nullable: false
    parameters:
      -
        in: path
        name: attachmentRelation_id
        description: 'The ID of the attachmentRelation.'
        example: 16
        required: true
        schema:
          type: integer
      -
        in: path
        name: attachable_relation_id
        description: 附件关联ID
        example: 16
        required: true
        schema:
          type: integer
