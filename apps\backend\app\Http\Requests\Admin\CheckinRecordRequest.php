<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CheckinRecordRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'checkin_config_id' => 'required|integer|exists:checkin_configs,id',
            'location' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'location_range' => 'nullable|integer|min:0',
            'attachment_id' => 'nullable|integer|exists:attachments,id',
            'content' => 'nullable|string|max:255'
        ];
    }

    public function attributes()
    {
        return [
            'checkin_config_id' => '打卡配置',
            'location' => '打卡地点',
            'position' => '打卡位置',
            'location_range' => '打卡范围',
            'attachment_id' => '打卡照片',
            'content' => '打卡备注'
        ];
    }
}
