name: 相关方管理
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/entities
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取相关方列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      name:
        name: name
        description: 相关方名称搜索
        required: false
        example: 测试公司
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tax_number:
        name: tax_number
        description: 税号搜索
        required: false
        example: 91110108MA01A2B3C4
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keywords:
        name: keywords
        description: 特征词搜索
        required: false
        example: 科技
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keyword:
        name: keyword
        description: 通用搜索关键词（同时搜索名称、税号、特征词）
        required: false
        example: 测试
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      entity_type:
        name: entity_type
        description: 相关方类型（字典code）
        required: false
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      name: 测试公司
      tax_number: 91110108MA01A2B3C4
      keywords: 科技
      keyword: 测试
      entity_type: manufacturer
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":63,"name":"\u5317\u4eac\u5eb7\u6cf0\u533b\u7597\u8bbe\u5907\u6709\u9650\u516c\u53f8","tax_number":"91090933724500949X","entity_type":"manufacturer","address":"38862 Ferne Locks Suite 058\nChristianshire, IA 97161","phone":"+12063337339","keywords":"deleniti distinctio eum","remark":"Id aut libero aliquam veniam.","created_by":1,"updated_by":1,"created_at":**********,"updated_at":**********},{"id":64,"name":"\u82cf\u5dde\u5965\u666e\u62d3\u6fc0\u5149\u79d1\u6280\u6709\u9650\u516c\u53f8","tax_number":"91445197905807351X","entity_type":"supplier","address":"2669 Wolff Trail\nBeierburgh, VA 78637","phone":"+****************","keywords":"ut et recusandae","remark":"Rerum ex repellendus assumenda et.","created_by":1,"updated_by":1,"created_at":**********,"updated_at":**********}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/entities
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建相关方
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 相关方名称
        required: true
        example: 测试科技有限公司
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tax_number:
        name: tax_number
        description: 税号
        required: false
        example: 91110108MA01A2B3C4
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      entity_type:
        name: entity_type
        description: 相关方类型（字典code）
        required: true
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      address:
        name: address
        description: 地址
        required: false
        example: 北京市海淀区中关村大街1号
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: false
        example: 010-12345678
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      keywords:
        name: keywords
        description: 特征词（10字以内）
        required: false
        example: 科技创新
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      remark:
        name: remark
        description: 备注
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contacts:
        name: contacts
        description: 联系人列表
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      brands:
        name: brands
        description: 品牌列表
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      attachments:
        name: attachments
        description: 'The <code>id</code> of an existing record in the attachments table.'
        required: false
        example:
          - 16
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'contacts[].id':
        name: 'contacts[].id'
        description: 'The <code>id</code> of an existing record in the entity_contacts table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'contacts[].name':
        name: 'contacts[].name'
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].phone':
        name: 'contacts[].phone'
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].position':
        name: 'contacts[].position'
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'contacts[].department':
        name: 'contacts[].department'
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'brands[].id':
        name: 'brands[].id'
        description: 'The <code>id</code> of an existing record in the entity_brands table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'brands[].name':
        name: 'brands[].name'
        description: 品牌名称
        required: true
        example: 华为
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'brands[].logo_id':
        name: 'brands[].logo_id'
        description: 品牌Logo附件ID
        required: false
        example: 123
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'brands[].description':
        name: 'brands[].description'
        description: 品牌描述
        required: false
        example: 全球领先的信息与通信技术解决方案供应商
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'brands[].sort_order':
        name: 'brands[].sort_order'
        description: 排序顺序
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 测试科技有限公司
      tax_number: 91110108MA01A2B3C4
      entity_type: manufacturer
      address: 北京市海淀区中关村大街1号
      phone: 010-12345678
      keywords: 科技创新
      remark: architecto
      contacts:
        - architecto
      brands:
        - architecto
      attachments:
        - 16
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/entities/export-template
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 导出相关方模板
      description: 导出包含所有字段的Excel模板文件，用于相关方批量导入
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/entities/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取相关方详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/entities/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新相关方
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 相关方名称
        required: true
        example: 测试科技有限公司
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tax_number:
        name: tax_number
        description: 税号
        required: false
        example: 91110108MA01A2B3C4
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      entity_type:
        name: entity_type
        description: 相关方类型（字典code）
        required: true
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      address:
        name: address
        description: 地址
        required: false
        example: 北京市海淀区中关村大街1号
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: false
        example: 010-12345678
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      keywords:
        name: keywords
        description: 特征词（10字以内）
        required: false
        example: 科技创新
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      remark:
        name: remark
        description: 备注
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contacts:
        name: contacts
        description: 联系人列表
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      brands:
        name: brands
        description: 品牌列表
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      attachments:
        name: attachments
        description: 'The <code>id</code> of an existing record in the attachments table.'
        required: false
        example:
          - 16
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'contacts[].id':
        name: 'contacts[].id'
        description: 联系人ID（更新时需要）
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'contacts[].name':
        name: 'contacts[].name'
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].phone':
        name: 'contacts[].phone'
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].position':
        name: 'contacts[].position'
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'contacts[].department':
        name: 'contacts[].department'
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'brands[].id':
        name: 'brands[].id'
        description: 品牌ID（更新时需要）
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'brands[].name':
        name: 'brands[].name'
        description: 品牌名称
        required: true
        example: 华为
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'brands[].logo_id':
        name: 'brands[].logo_id'
        description: 品牌Logo附件ID
        required: false
        example: 123
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'brands[].description':
        name: 'brands[].description'
        description: 品牌描述
        required: false
        example: 全球领先的信息与通信技术解决方案供应商
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'brands[].sort_order':
        name: 'brands[].sort_order'
        description: 排序顺序
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 测试科技有限公司
      tax_number: 91110108MA01A2B3C4
      entity_type: manufacturer
      address: 北京市海淀区中关村大街1号
      phone: 010-12345678
      keywords: 科技创新
      remark: architecto
      contacts:
        - architecto
      brands:
        - architecto
      attachments:
        - 16
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/entities/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除相关方
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/entities/{entity_id}/contacts'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取相关方的联系人列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      entity: 1
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":32,"entity_id":65,"name":"Morgan Hirthe","phone":"************","position":"\u4e34\u5e8a\u5de5\u7a0b\u5e08","department":"\u5ba2\u670d\u90e8","created_by":1,"updated_by":1,"created_at":**********,"updated_at":**********},{"id":33,"entity_id":66,"name":"Dr. Alan Green","phone":"************","position":"\u8bbe\u5907\u79d1\u5de5\u7a0b\u5e08","department":"\u91c7\u8d2d\u90e8","created_by":1,"updated_by":1,"created_at":**********,"updated_at":**********}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":10,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/entities/{entity_id}/contacts'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建联系人
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      position:
        name: position
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      department:
        name: department
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 张三
      phone: '13800138000'
      position: 总经理
      department: 管理部
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/entities/{entity_id}/contacts/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新联系人
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 'The ID of the contact.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact:
        name: contact
        description: 联系人ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      id: 1
      entity: 1
      contact: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      position:
        name: position
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      department:
        name: department
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 张三
      phone: '13800138000'
      position: 总经理
      department: 管理部
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/entities/{entity_id}/contacts/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除联系人
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 'The ID of the contact.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact:
        name: contact
        description: 联系人ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      id: 1
      entity: 1
      contact: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/entities/{entity_id}/brands'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取相关方的品牌列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      entity: 1
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":63,"entity_id":67,"name":"GE Healthcare","logo":null,"logo_id":null,"description":"Quidem nostrum qui commodi incidunt iure odit.","sort_order":765,"created_by":1,"updated_by":1,"created_at":**********,"updated_at":**********},{"id":64,"entity_id":68,"name":"\u7406\u90a6\u4eea\u5668","logo":null,"logo_id":null,"description":"Officia est dignissimos neque blanditiis odio veritatis excepturi.","sort_order":721,"created_by":1,"updated_by":1,"created_at":**********,"updated_at":**********}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":10,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/entities/{entity_id}/brands'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建品牌
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 品牌名称
        required: true
        example: 华为
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 品牌描述
        required: false
        example: 全球领先的信息与通信技术解决方案供应商
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      logo_id:
        name: logo_id
        description: 品牌Logo附件ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      sort_order:
        name: sort_order
        description: 排序顺序
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 华为
      description: 全球领先的信息与通信技术解决方案供应商
      logo_id: 1
      sort_order: 100
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/entities/{entity_id}/brands/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新品牌
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 'The ID of the brand.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      brand:
        name: brand
        description: 品牌ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      id: 1
      entity: 1
      brand: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 品牌名称
        required: true
        example: 华为
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 品牌描述
        required: false
        example: 全球领先的信息与通信技术解决方案供应商
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      logo_id:
        name: logo_id
        description: 品牌Logo附件ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      sort_order:
        name: sort_order
        description: 排序顺序
        required: false
        example: 100
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 华为
      description: 全球领先的信息与通信技术解决方案供应商
      logo_id: 1
      sort_order: 100
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/entities/{entity_id}/brands/{id}'
    metadata:
      groupName: 相关方管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除品牌
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 'The ID of the brand.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 相关方ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      brand:
        name: brand
        description: 品牌ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 1
      id: 1
      entity: 1
      brand: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 3|RFZ3BjAdJ9f9TwW8gwhZ5WDIxUaToDXi1RxIPxyB2dc02acb'
    controller: null
    method: null
    route: null
    custom: []
